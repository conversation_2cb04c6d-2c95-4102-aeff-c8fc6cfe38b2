<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - مؤسسة النور التربوي</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <img src="logo" alt="شعار مؤسسة النور التربوي" class="logo">
        <h1>مؤسسة النور التربوي للتعليم الخصوصي</h1>
        <nav>
            <ul>
                <li><a href="index.html">لوحة التحكم</a></li>
                <li><a href="students.html">إدارة التلاميذ</a></li>
                <li><a href="teachers.html">إدارة الأساتذة</a></li>
                <li><a href="staff.html">إدارة الموظفين</a></li>
                <li><a href="groups.html">إدارة المستويات والأفواج</a></li>
                <li><a href="financial.html">الإجراءات المالية</a></li>
                <li><a href="payments.html">عمليات الدفع</a></li>
                <li><a href="whatsapp.html">إدارة WhatsApp</a></li>
                <li><a href="import-export.html">الاستيراد والتصدير</a></li>
                <li><a href="users.html" class="active">إدارة المستخدمين</a></li>
                <li><a href="activities.html">إدارة الأنشطة</a></li>
                <li><a href="settings.html">الإعدادات</a></li>
            </ul>
        </nav>
        <div class="user-info">
            <span class="current-user-name">المستخدم</span>
            <span class="current-user-role">الدور</span>
            <button onclick="window.authSystem.logout()" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </button>
        </div>
    </header>

    <main>
        <section id="users-management">
            <h2><i class="fas fa-users-cog"></i> إدارة المستخدمين والصلاحيات</h2>
            
            <!-- إحصائيات المستخدمين -->
            <div class="users-stats">
                <div class="stat-card admins">
                    <div class="stat-icon">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="stat-info">
                        <h3>المديرون</h3>
                        <p id="admins-count">0</p>
                    </div>
                </div>
                <div class="stat-card teachers">
                    <div class="stat-icon">
                        <i class="fas fa-chalkboard-teacher"></i>
                    </div>
                    <div class="stat-info">
                        <h3>الأساتذة</h3>
                        <p id="teachers-count">0</p>
                    </div>
                </div>
                <div class="stat-card staff">
                    <div class="stat-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <div class="stat-info">
                        <h3>الموظفون</h3>
                        <p id="staff-count">0</p>
                    </div>
                </div>
                <div class="stat-card online">
                    <div class="stat-icon">
                        <i class="fas fa-circle"></i>
                    </div>
                    <div class="stat-info">
                        <h3>متصل الآن</h3>
                        <p id="online-count">0</p>
                    </div>
                </div>
            </div>

            <!-- أدوات الإدارة -->
            <div class="users-controls">
                <button id="add-user-btn" class="primary-btn" data-permission="users_create">
                    <i class="fas fa-user-plus"></i> إضافة مستخدم جديد
                </button>
                <button id="bulk-actions-btn" class="secondary-btn" data-permission="users_update">
                    <i class="fas fa-tasks"></i> إجراءات جماعية
                </button>
                <button id="export-users-btn" class="success-btn" data-permission="users_read">
                    <i class="fas fa-download"></i> تصدير قائمة المستخدمين
                </button>
                <button id="user-activities-btn" class="info-btn" data-permission="users_read">
                    <i class="fas fa-history"></i> سجل الأنشطة
                </button>
            </div>

            <!-- فلاتر البحث -->
            <div class="users-filters">
                <div class="filter-group">
                    <label for="role-filter">الدور:</label>
                    <select id="role-filter">
                        <option value="">جميع الأدوار</option>
                        <option value="admin">مدير</option>
                        <option value="teacher">أستاذ</option>
                        <option value="staff">موظف</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="status-filter">الحالة:</label>
                    <select id="status-filter">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="search-users">البحث:</label>
                    <input type="text" id="search-users" placeholder="ابحث بالاسم أو اسم المستخدم...">
                </div>
                <button id="apply-filters-btn" class="filter-btn">
                    <i class="fas fa-filter"></i> تطبيق
                </button>
                <button id="clear-filters-btn" class="clear-btn">
                    <i class="fas fa-times"></i> مسح
                </button>
            </div>

            <!-- جدول المستخدمين -->
            <div class="users-table-container">
                <table id="users-table" class="data-table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="select-all-users"></th>
                            <th>الصورة</th>
                            <th>الاسم</th>
                            <th>اسم المستخدم</th>
                            <th>الدور</th>
                            <th>البريد الإلكتروني</th>
                            <th>الهاتف</th>
                            <th>آخر تسجيل دخول</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="users-table-body">
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </tbody>
                </table>
            </div>
        </section>
    </main>

    <!-- نافذة إضافة/تعديل مستخدم -->
    <div id="user-modal" class="modal" style="display: none;">
        <div class="modal-content large">
            <div class="modal-header">
                <h3 id="user-modal-title"><i class="fas fa-user-plus"></i> إضافة مستخدم جديد</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="user-form">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="user-name">الاسم الكامل:</label>
                            <input type="text" id="user-name" required>
                        </div>
                        <div class="form-group">
                            <label for="user-username">اسم المستخدم:</label>
                            <input type="text" id="user-username" required>
                        </div>
                        <div class="form-group">
                            <label for="user-email">البريد الإلكتروني:</label>
                            <input type="email" id="user-email" required>
                        </div>
                        <div class="form-group">
                            <label for="user-phone">رقم الهاتف:</label>
                            <input type="tel" id="user-phone" required>
                        </div>
                        <div class="form-group">
                            <label for="user-role">الدور:</label>
                            <select id="user-role" required>
                                <option value="">اختر الدور</option>
                                <option value="admin">مدير</option>
                                <option value="teacher">أستاذ</option>
                                <option value="staff">موظف</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="user-password">كلمة المرور:</label>
                            <input type="password" id="user-password" required>
                        </div>
                    </div>

                    <div class="permissions-section">
                        <h4><i class="fas fa-key"></i> الصلاحيات</h4>
                        <div class="permissions-grid" id="permissions-grid">
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </div>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="user-active" checked>
                            المستخدم نشط
                        </label>
                    </div>

                    <div class="modal-actions">
                        <button type="submit" class="primary-btn">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                        <button type="button" class="cancel-btn">إلغاء</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة سجل الأنشطة -->
    <div id="activities-modal" class="modal" style="display: none;">
        <div class="modal-content large">
            <div class="modal-header">
                <h3><i class="fas fa-history"></i> سجل أنشطة المستخدمين</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="activities-filters">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label for="activity-user-filter">المستخدم:</label>
                            <select id="activity-user-filter">
                                <option value="">جميع المستخدمين</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="activity-action-filter">النشاط:</label>
                            <select id="activity-action-filter">
                                <option value="">جميع الأنشطة</option>
                                <option value="login">تسجيل دخول</option>
                                <option value="logout">تسجيل خروج</option>
                                <option value="create">إنشاء</option>
                                <option value="update">تعديل</option>
                                <option value="delete">حذف</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="activity-date-from">من تاريخ:</label>
                            <input type="date" id="activity-date-from">
                        </div>
                        <div class="filter-group">
                            <label for="activity-date-to">إلى تاريخ:</label>
                            <input type="date" id="activity-date-to">
                        </div>
                    </div>
                    <div class="filter-actions">
                        <button id="apply-activity-filters-btn" class="primary-btn">تطبيق الفلاتر</button>
                        <button id="export-activities-btn" class="success-btn">تصدير السجل</button>
                    </div>
                </div>
                <div id="activities-list" class="activities-table"></div>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div id="delete-user-modal" class="modal" style="display: none;">
        <div class="modal-content small">
            <div class="modal-header">
                <h3><i class="fas fa-exclamation-triangle"></i> تأكيد الحذف</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذا المستخدم؟</p>
                <p class="warning-text">لا يمكن التراجع عن هذا الإجراء!</p>
                <div class="modal-actions">
                    <button id="confirm-delete-btn" class="danger-btn">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                    <button class="cancel-btn">إلغاء</button>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p>مؤسسة النور التربوي - نظام إدارة شامل للمؤسسات التعليمية.</p>
    </footer>

    <script src="auth.js"></script>
    <script src="sync.js"></script>
    <script src="script.js"></script>
    <script src="users.js"></script>
</body>
</html>
