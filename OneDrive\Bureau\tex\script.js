document.addEventListener('DOMContentLoaded', () => {
    // =================================================================
    //                      Constants & Storage
    // =================================================================
    const ACADEMIC_LEVELS = [
        "التمهيدي الأول", "التمهيدي الثاني", "التمهيدي الثالث",
        "الأول ابتدائي", "الثاني ابتدائي", "الثالث ابتدائي", "الرابع ابتدائي", "الخامس ابتدائي", "السادس ابتدائي",
        "الأولى إعدادي", "الثانية إعدادي", "الثالثة إعدادي"
    ];

    // أشهر السنة الدراسية
    const ACADEMIC_MONTHS = [
        "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر", "يناير",
        "فبراير", "مارس", "أبريل", "مايو", "يونيو"
    ];

    // حالات الدفع
    const PAYMENT_STATUS = {
        PAID: 'مدفوع',
        PARTIAL: 'جزئي',
        UNPAID: 'غير مدفوع'
    };

    function getFromStorage(key) {
        return JSON.parse(localStorage.getItem(key)) || [];
    }

    function saveToStorage(key, data) {
        localStorage.setItem(key, JSON.stringify(data));
    }

    let students = getFromStorage('sbea_students');
    let groups = getFromStorage('sbea_groups');
    let teachers = getFromStorage('sbea_teachers');
    let activities = getFromStorage('sbea_activities');
    let curriculum = getFromStorage('sbea_curriculum');
    let inspections = getFromStorage('sbea_inspections');
    let adminNotes = getFromStorage('sbea_admin_notes');
    let competitions = getFromStorage('sbea_competitions');
    let adminStaff = getFromStorage('sbea_admin_staff');
    let guards = getFromStorage('sbea_guards');
    let cleaners = getFromStorage('sbea_cleaners');
    let teacherSalaries = getFromStorage('sbea_teacher_salaries');

    // قائمة شاملة لجميع المواد الدراسية
    const ALL_SUBJECTS = [
        "التربية الإسلامية",
        "النشاط العلمي",
        "التربية الفنية",
        "الرياضيات",
        "التربية البدنية",
        "اللغة الإنجليزية",
        "اللغة الفرنسية",
        "اللغة العربية",
        "الاجتماعيات",
        "الفيزياء والكيمياء",
        "علوم الحياة والأرض"
    ];

    const SUBJECTS_BY_LEVEL = {
        "الأول ابتدائي": {
            "اللغة العربية": ["التعبير الكتابي", "تمارين كتابية", "القراءة", "الإملاء", "الخط", "الاستماع والتحدث"],
            "اللغة الفرنسية": ["Dictée", "Activités orales", "Poésie", "Lecture", "Écriture/Copie", "Exercices écrits", "Projet de classe"],
            "مواد أخرى": ["التربية الإسلامية", "النشاط العلمي", "التربية الفنية", "الرياضيات", "التربية البدنية"]
        },
        "الابتدائي (2-6)": {
            "اللغة العربية": ["التعبير الكتابي", "تمارين كتابية", "القراءة", "الإملاء", "الخط", "الاستماع والتحدث"],
            "اللغة الفرنسية": ["Dictée", "Activités orales", "Poésie", "Lecture", "Écriture/Copie", "Exercices écrits", "Projet de classe"],
            "مواد أخرى": ["التربية الإسلامية", "النشاط العلمي", "التربية الفنية", "الرياضيات", "التربية البدنية", "اللغة الإنجليزية"]
        },
        "الإعدادي": {
            "مواد": ["الاجتماعيات", "التربية الإسلامية", "التربية البدنية", "الرياضيات", "الفيزياء والكيمياء", "اللغة الإنجليزية", "اللغة العربية", "اللغة الفرنسية", "علوم الحياة والأرض"]
        }
    };

    // =================================================================
    //                      Page Routing
    // =================================================================
    const currentPage = window.location.pathname.split('/').pop();

    if (currentPage === 'index.html' || currentPage === '') {
        initDashboardPage();
    } else if (currentPage === 'students.html') {
        initStudentsPage();
    } else if (currentPage === 'teachers.html') {
        initTeachersPage();
    } else if (currentPage === 'staff.html') {
        initStaffPage();
    } else if (currentPage === 'groups.html') {
        initGroupsPage();
    } else if (currentPage === 'financial.html') {
        initFinancialPage();
    } else if (currentPage === 'activities.html') {
        initActivitiesPage();
    } else if (currentPage === 'settings.html') {
        initSettingsPage();
    }

    // =================================================================
    //                      Dashboard Page
    // =================================================================
    function initDashboardPage() {
        updateDashboardStats();

        // Initialize search functionality
        initSearchFunctionality();

        // Add test data if storage is empty (for testing purposes)
        if (students.length === 0) {
            // دالة لإنشاء بيانات الواجبات الشهرية
            function createMonthlyPayments(monthlyFee) {
                const payments = {};
                ACADEMIC_MONTHS.forEach(month => {
                    payments[month] = {
                        status: 'غير مدفوع',
                        amount: 0,
                        remaining: monthlyFee,
                        dueAmount: monthlyFee
                    };
                });
                return payments;
            }

            students = [
                {
                    name: "أحمد محمد علي",
                    level: "الثالث ابتدائي",
                    group: "فوج أ",
                    fee: "5000",
                    paid: true, // للتوافق مع النظام القديم
                    phone: "0555123456",
                    barcode: "000001",
                    picture: null,
                    hasTransport: true,
                    transportFee: "2000",
                    monthlyPayments: createMonthlyPayments(5000),
                    additionalFees: []
                },
                {
                    name: "فاطمة الزهراء",
                    level: "الخامس ابتدائي",
                    group: "فوج ب",
                    fee: "6000",
                    paid: false,
                    phone: "0666789012",
                    barcode: "000002",
                    picture: null,
                    hasTransport: false,
                    transportFee: "0",
                    monthlyPayments: createMonthlyPayments(6000),
                    additionalFees: []
                },
                {
                    name: "يوسف عبد الرحمن",
                    level: "الأولى إعدادي",
                    group: "فوج أ",
                    fee: "7000",
                    paid: true,
                    phone: "0777345678",
                    barcode: "000003",
                    picture: null,
                    hasTransport: true,
                    transportFee: "2500",
                    monthlyPayments: createMonthlyPayments(7000),
                    additionalFees: []
                }
            ];

            // تحديث بعض الدفعات للاختبار
            students[0].monthlyPayments["سبتمبر"] = { status: 'مدفوع', amount: 5000, remaining: 0, dueAmount: 5000 };
            students[0].monthlyPayments["أكتوبر"] = { status: 'جزئي', amount: 3000, remaining: 2000, dueAmount: 5000 };

            students[2].monthlyPayments["سبتمبر"] = { status: 'مدفوع', amount: 7000, remaining: 0, dueAmount: 7000 };

            saveToStorage('sbea_students', students);
        }

        if (teachers.length === 0) {
            teachers = [
                {
                    name: "الأستاذ محمد الأمين",
                    salary: "50000",
                    tasks: "تدريس الرياضيات والفيزياء",
                    levels: ["الثالث ابتدائي", "الخامس ابتدائي"],
                    groups: ["فوج أ", "فوج ب"],
                    subjects: ["الرياضيات", "الفيزياء والكيمياء"],
                    monthlySalaries: createMonthlySalaries(50000)
                },
                {
                    name: "الأستاذة خديجة بن علي",
                    salary: "45000",
                    tasks: "تدريس اللغة العربية والتربية الإسلامية",
                    levels: ["الأولى إعدادي", "الثانية إعدادي"],
                    groups: ["فوج أ"],
                    subjects: ["اللغة العربية", "التربية الإسلامية"],
                    monthlySalaries: createMonthlySalaries(45000)
                },
                {
                    name: "الأستاذ عبد الرحمن الصادق",
                    salary: "42000",
                    tasks: "تدريس اللغة الفرنسية والإنجليزية",
                    levels: ["الخامس ابتدائي", "السادس ابتدائي"],
                    groups: ["فوج أ", "فوج ب"],
                    subjects: ["اللغة الفرنسية", "اللغة الإنجليزية"],
                    monthlySalaries: createMonthlySalaries(42000)
                },
                {
                    name: "الأستاذة فاطمة الزهراء",
                    salary: "38000",
                    tasks: "تدريس النشاط العلمي والتربية الفنية",
                    levels: ["الثالث ابتدائي", "الرابع ابتدائي"],
                    groups: ["فوج أ"],
                    subjects: ["النشاط العلمي", "التربية الفنية"],
                    monthlySalaries: createMonthlySalaries(38000)
                }
            ];
            saveToStorage('sbea_teachers', teachers);
        }

        if (groups.length === 0) {
            groups = [
                { level: "الثالث ابتدائي", name: "فوج أ" },
                { level: "الثالث ابتدائي", name: "فوج ب" },
                { level: "الخامس ابتدائي", name: "فوج أ" },
                { level: "الخامس ابتدائي", name: "فوج ب" },
                { level: "الأولى إعدادي", name: "فوج أ" },
                { level: "الأولى إعدادي", name: "فوج ب" },
                { level: "الثانية إعدادي", name: "فوج أ" },
                { level: "الثانية إعدادي", name: "فوج ب" }
            ];
            saveToStorage('sbea_groups', groups);
        }

        // Update dashboard stats with new data
        updateDashboardStats();
    }

    function updateDashboardStats() {
        // الإحصائيات الأساسية
        document.getElementById('total-students').textContent = students.length;
        document.getElementById('total-groups').textContent = groups.length;
        document.getElementById('total-teachers').textContent = teachers.length;

        // حساب الإحصائيات المالية المتقدمة
        let totalDue = 0;
        let totalPaid = 0;
        let totalRemaining = 0;
        let paidCount = 0;
        let partialCount = 0;
        let unpaidCount = 0;
        let transportStudents = 0;
        let transportFees = 0;

        students.forEach(student => {
            // حساب النقل
            if (student.hasTransport) {
                transportStudents++;
                transportFees += parseFloat(student.transportFee || 0);
            }

            // حساب الدفعات الشهرية
            if (student.monthlyPayments) {
                Object.values(student.monthlyPayments).forEach(payment => {
                    totalDue += payment.dueAmount || 0;
                    totalPaid += payment.amount || 0;
                    totalRemaining += payment.remaining || 0;

                    if (payment.status === 'مدفوع') paidCount++;
                    else if (payment.status === 'جزئي') partialCount++;
                    else unpaidCount++;
                });
            } else {
                // للتوافق مع النظام القديم
                const monthlyFee = parseFloat(student.fee || 0);
                totalDue += monthlyFee * 10; // 10 أشهر
                if (student.paid) {
                    totalPaid += monthlyFee * 10;
                    paidCount += 10;
                } else {
                    totalRemaining += monthlyFee * 10;
                    unpaidCount += 10;
                }
            }
        });

        // تحديث الإحصائيات الأساسية
        document.getElementById('total-fees').textContent = totalDue.toLocaleString() + ' DHS';
        document.getElementById('paid-fees').textContent = totalPaid.toLocaleString() + ' DHS';
        document.getElementById('remaining-fees').textContent = totalRemaining.toLocaleString() + ' DHS';

        // حساب النسب المئوية
        const totalPayments = paidCount + partialCount + unpaidCount;
        const paidPercentage = totalPayments > 0 ? (paidCount / totalPayments * 100).toFixed(1) : 0;
        const partialPercentage = totalPayments > 0 ? (partialCount / totalPayments * 100).toFixed(1) : 0;
        const unpaidPercentage = totalPayments > 0 ? (unpaidCount / totalPayments * 100).toFixed(1) : 0;

        // تحديث أشرطة التقدم
        document.getElementById('paid-percentage').style.width = paidPercentage + '%';
        document.getElementById('partial-percentage').style.width = partialPercentage + '%';
        document.getElementById('unpaid-percentage').style.width = unpaidPercentage + '%';

        document.getElementById('paid-percent-text').textContent = paidPercentage + '%';
        document.getElementById('partial-percent-text').textContent = partialPercentage + '%';
        document.getElementById('unpaid-percent-text').textContent = unpaidPercentage + '%';

        // إحصائيات النقل
        document.getElementById('transport-students').textContent = transportStudents;
        document.getElementById('transport-fees').textContent = transportFees.toLocaleString() + ' DHS';
        const transportPercentage = students.length > 0 ? (transportStudents / students.length * 100).toFixed(1) : 0;
        document.getElementById('transport-percentage').textContent = transportPercentage + '%';

        // توزيع التلاميذ حسب المستوى
        updateLevelDistribution();
    }

    function updateLevelDistribution() {
        const levelCounts = {};
        students.forEach(student => {
            levelCounts[student.level] = (levelCounts[student.level] || 0) + 1;
        });

        const levelDistributionDiv = document.getElementById('level-distribution');
        let html = '';

        Object.entries(levelCounts).forEach(([level, count]) => {
            const percentage = students.length > 0 ? (count / students.length * 100).toFixed(1) : 0;
            html += `
                <div class="level-item">
                    <span class="level-name">${level}</span>
                    <div class="level-bar">
                        <div class="level-fill" style="width: ${percentage}%"></div>
                    </div>
                    <span class="level-count">${count} (${percentage}%)</span>
                </div>
            `;
        });

        levelDistributionDiv.innerHTML = html;
    }

    function initSearchFunctionality() {
        const searchNameInput = document.getElementById('search-name');
        const searchLevelInput = document.getElementById('search-level');
        const searchBarcodeInput = document.getElementById('search-barcode');
        const searchTypeInput = document.getElementById('search-type');
        const searchBtn = document.getElementById('search-btn');
        const clearSearchBtn = document.getElementById('clear-search-btn');
        const searchResults = document.getElementById('search-results');

        // Populate levels dropdown
        ACADEMIC_LEVELS.forEach(level => {
            const option = document.createElement('option');
            option.value = level;
            option.textContent = level;
            searchLevelInput.appendChild(option);
        });

        function performSearch() {
            const nameQuery = searchNameInput.value.toLowerCase().trim();
            const levelQuery = searchLevelInput.value;
            const barcodeQuery = searchBarcodeInput.value.trim();
            const typeQuery = searchTypeInput.value;

            let results = [];

            // Search students
            if (typeQuery === 'all' || typeQuery === 'students') {
                const studentResults = students.filter(student => {
                    const nameMatch = !nameQuery || student.name.toLowerCase().includes(nameQuery);
                    const levelMatch = !levelQuery || student.level === levelQuery;
                    const barcodeMatch = !barcodeQuery || student.barcode.includes(barcodeQuery);
                    return nameMatch && levelMatch && barcodeMatch;
                }).map(student => ({
                    type: 'student',
                    name: student.name,
                    level: student.level,
                    group: student.group,
                    barcode: student.barcode,
                    phone: student.phone,
                    fee: student.fee,
                    paid: student.paid,
                    picture: student.picture
                }));
                results = results.concat(studentResults);
            }

            // Search teachers
            if (typeQuery === 'all' || typeQuery === 'teachers') {
                const teacherResults = teachers.filter(teacher => {
                    const nameMatch = !nameQuery || teacher.name.toLowerCase().includes(nameQuery);
                    const levelMatch = !levelQuery || teacher.levels.includes(levelQuery);
                    return nameMatch && levelMatch;
                }).map(teacher => ({
                    type: 'teacher',
                    name: teacher.name,
                    levels: teacher.levels,
                    groups: teacher.groups,
                    subjects: teacher.subjects,
                    salary: teacher.salary,
                    tasks: teacher.tasks
                }));
                results = results.concat(teacherResults);
            }

            displayResults(results);
        }

        function displayResults(results) {
            if (results.length === 0) {
                searchResults.innerHTML = '<p class="no-results">لم يتم العثور على نتائج مطابقة للبحث.</p>';
                return;
            }

            let html = '<h3>نتائج البحث:</h3><div class="results-grid">';

            results.forEach(item => {
                if (item.type === 'student') {
                    html += `
                        <div class="result-card student-card">
                            <div class="result-header">
                                <img src="${item.picture || 'logo.jpg'}" alt="صورة ${item.name}" class="result-image">
                                <h4>${item.name}</h4>
                                <span class="result-type">تلميذ</span>
                            </div>
                            <div class="result-details">
                                <p><strong>المستوى:</strong> ${item.level}</p>
                                <p><strong>الفوج:</strong> ${item.group}</p>
                                <p><strong>الرمز الشريطي:</strong> ${item.barcode}</p>
                                <p><strong>الهاتف:</strong> ${item.phone}</p>
                                <p><strong>المستحقات:</strong> ${item.fee} DHS</p>
                                <p class="${item.paid ? 'paid' : 'unpaid'}"><strong>الحالة:</strong> ${item.paid ? 'مدفوع' : 'غير مدفوع'}</p>
                            </div>
                        </div>
                    `;
                } else if (item.type === 'teacher') {
                    html += `
                        <div class="result-card teacher-card">
                            <div class="result-header">
                                <img src="logo.jpg" alt="صورة ${item.name}" class="result-image">
                                <h4>${item.name}</h4>
                                <span class="result-type">أستاذ</span>
                            </div>
                            <div class="result-details">
                                <p><strong>المستويات:</strong> ${item.levels.join(', ')}</p>
                                <p><strong>الأفواج:</strong> ${item.groups.join(', ')}</p>
                                <p><strong>المواد:</strong> ${item.subjects.join(', ')}</p>
                                <p><strong>الراتب:</strong> ${item.salary || 'غير محدد'}</p>
                                <p><strong>المهام:</strong> ${item.tasks || 'غير محددة'}</p>
                            </div>
                        </div>
                    `;
                }
            });

            html += '</div>';
            searchResults.innerHTML = html;
        }

        function clearSearch() {
            searchNameInput.value = '';
            searchLevelInput.value = '';
            searchBarcodeInput.value = '';
            searchTypeInput.value = 'all';
            searchResults.innerHTML = '';
        }

        // Event listeners
        searchBtn.addEventListener('click', performSearch);
        clearSearchBtn.addEventListener('click', clearSearch);

        // Real-time search on input
        searchNameInput.addEventListener('input', performSearch);
        searchLevelInput.addEventListener('change', performSearch);
        searchBarcodeInput.addEventListener('input', performSearch);
        searchTypeInput.addEventListener('change', performSearch);
    }

    // =================================================================
    //                      Students Page
    // =================================================================
    function initStudentsPage() {
        const studentForm = document.getElementById('add-student-form');
        const studentTableBody = document.querySelector('#student-table tbody');
        const searchNameInput = document.getElementById('search-name');
        const searchLevelInput = document.getElementById('search-level');
        const searchBarcodeInput = document.getElementById('search-barcode');
        const printStudentsBtn = document.getElementById('print-students-btn');
        const studentLevelSelect = document.getElementById('student-level');
        const studentGroupSelect = document.getElementById('student-group');
        const importExcelBtn = document.getElementById('import-excel-btn');
        const excelFileInput = document.getElementById('excel-file-input');
        const downloadTemplateBtn = document.getElementById('download-template-btn');

        function populateLevelsDropdown(selectElement) {
            ACADEMIC_LEVELS.forEach(level => {
                const option = document.createElement('option');
                option.value = level;
                option.textContent = level;
                selectElement.appendChild(option.cloneNode(true));
            });
             // Also populate search dropdown
            ACADEMIC_LEVELS.forEach(level => {
                const option = document.createElement('option');
                option.value = level;
                option.textContent = level;
                searchLevelInput.appendChild(option);
            });
        }

        function updateGroupsDropdown(selectedLevel) {
            studentGroupSelect.innerHTML = '<option value="">اختر الفوج</option>';
            const filteredGroups = groups.filter(g => g.level === selectedLevel);
            filteredGroups.forEach(group => {
                const option = document.createElement('option');
                option.value = group.name;
                option.textContent = group.name;
                studentGroupSelect.appendChild(option);
            });
        }

        studentLevelSelect.addEventListener('change', () => {
            updateGroupsDropdown(studentLevelSelect.value);
        });

        function renderStudents() {
            const nameFilter = searchNameInput.value.toLowerCase();
            const levelFilter = searchLevelInput.value;
            const barcodeFilter = searchBarcodeInput.value;

            const filteredStudents = students.filter(s => {
                const nameMatch = s.name.toLowerCase().includes(nameFilter);
                const levelMatch = !levelFilter || s.level === levelFilter;
                const barcodeMatch = !barcodeFilter || s.barcode.includes(barcodeFilter);
                return nameMatch && levelMatch && barcodeMatch;
            });

            studentTableBody.innerHTML = '';
            filteredStudents.forEach((student, index) => {
                addStudentToTable(student, index + 1);
            });
        }

        function addStudentToTable(student, serialNumber) {
            const { name, level, group, fee, phone, barcode, picture, hasTransport, transportFee, monthlyPayments } = student;
            const paddedSerialNumber = serialNumber.toString().padStart(6, '0');
            const row = document.createElement('tr');
            // Use barcode as the unique identifier
            row.dataset.barcode = barcode;

            // حساب إجمالي المدفوع والباقي
            let totalPaid = 0;
            let totalRemaining = 0;

            if (monthlyPayments) {
                Object.values(monthlyPayments).forEach(payment => {
                    totalPaid += payment.amount || 0;
                    totalRemaining += payment.remaining || 0;
                });
            }

            row.innerHTML = `
                <td><img src="${picture || `${paddedSerialNumber}.jpg`}" alt="صورة شخصية" class="student-picture-thumbnail" onerror="this.src='logo.jpg'"></td>
                <td>${paddedSerialNumber}</td>
                <td>${name}</td>
                <td>${level}</td>
                <td>${group}</td>
                <td>${fee} DHS</td>
                <td class="${hasTransport ? 'transport-yes' : 'transport-no'}">${hasTransport ? `نعم (${transportFee} DHS)` : 'لا'}</td>
                <td class="paid">${totalPaid.toLocaleString()} DHS</td>
                <td class="${totalRemaining > 0 ? 'unpaid' : 'paid'}">${totalRemaining.toLocaleString()} DHS</td>
                <td>${phone}</td>
                <td><svg id="barcode-${barcode}"></svg></td>
                <td>
                    <button class="edit-btn">تعديل</button>
                    <button class="delete-btn">حذف</button>
                    <button class="payments-btn">الدفعات</button>
                    <button class="whatsapp-btn">WhatsApp</button>
                </td>
            `;
            studentTableBody.appendChild(row);
            // Use the student's own barcode property for the JsBarcode
            if (barcode) JsBarcode(`#barcode-${barcode}`, barcode, { height: 40, displayValue: true, margin: 0, fontSize: 14 });

            row.querySelector('.delete-btn').addEventListener('click', (e) => {
                const targetBarcode = e.target.closest('tr').dataset.barcode;
                const studentIndex = students.findIndex(s => s.barcode === targetBarcode);
                if (studentIndex > -1) {
                    students.splice(studentIndex, 1);
                    saveToStorage('sbea_students', students);
                    renderStudents();
                }
            });
            row.querySelector('.payments-btn').addEventListener('click', (e) => {
                const targetBarcode = e.target.closest('tr').dataset.barcode;
                const studentIndex = students.findIndex(s => s.barcode === targetBarcode);
                if (studentIndex > -1) {
                    openPaymentsModal(students[studentIndex]);
                }
            });
            row.querySelector('.whatsapp-btn').addEventListener('click', () => {
                const internationalPhone = phone.startsWith('0') ? `213${phone.substring(1)}` : phone;
                window.open(`https://wa.me/${internationalPhone}`, '_blank');
            });
            row.querySelector('.edit-btn').addEventListener('click', e => {
                const targetBarcode = e.target.closest('tr').dataset.barcode;
                const studentIndex = students.findIndex(s => s.barcode === targetBarcode);
                if (studentIndex > -1) {
                    openEditModal(students[studentIndex]);
                }
            });
        }

        // إضافة معالج لإظهار/إخفاء حقل رسوم النقل
        const transportCheckbox = document.getElementById('student-transport');
        const transportFeeContainer = document.getElementById('transport-fee-container');

        transportCheckbox.addEventListener('change', () => {
            transportFeeContainer.style.display = transportCheckbox.checked ? 'block' : 'none';
        });

        studentForm.addEventListener('submit', async e => {
            e.preventDefault();
            const pictureInput = document.getElementById('student-picture');
            let pictureDataUrl = null;

            if (pictureInput.files && pictureInput.files[0]) {
                try {
                    pictureDataUrl = await new Promise((resolve, reject) => {
                        const reader = new FileReader();
                        reader.onload = () => resolve(reader.result);
                        reader.onerror = reject;
                        reader.readAsDataURL(pictureInput.files[0]);
                    });
                } catch (error) {
                    console.error("Error reading file:", error);
                    alert("حدث خطأ أثناء تحميل الصورة.");
                    return;
                }
            }

            const monthlyFee = parseFloat(document.getElementById('student-fee').value.trim());
            const hasTransport = document.getElementById('student-transport').checked;
            const transportFee = hasTransport ? parseFloat(document.getElementById('student-transport-fee').value || 0) : 0;

            // إنشاء بيانات الواجبات الشهرية
            const monthlyPayments = {};
            ACADEMIC_MONTHS.forEach(month => {
                monthlyPayments[month] = {
                    status: 'غير مدفوع',
                    amount: 0,
                    remaining: monthlyFee,
                    dueAmount: monthlyFee
                };
            });

            const newStudent = {
                name: document.getElementById('student-name').value.trim(),
                level: document.getElementById('student-level').value,
                group: document.getElementById('student-group').value,
                fee: monthlyFee.toString(),
                paid: false, // للتوافق مع النظام القديم
                phone: document.getElementById('student-phone').value.trim(),
                picture: pictureDataUrl,
                hasTransport: hasTransport,
                transportFee: transportFee.toString(),
                monthlyPayments: monthlyPayments,
                additionalFees: [],
                barcode: '' // Will be set after pushing to the array
            };

            students.push(newStudent);
            // Assign barcode based on the final index + 1 (the serial number)
            const newIndex = students.length;
            newStudent.barcode = newIndex.toString().padStart(6, '0');

            saveToStorage('sbea_students', students);
            renderStudents();
            studentForm.reset();
            studentGroupSelect.innerHTML = '<option value="">اختر الفوج</option>';
            transportFeeContainer.style.display = 'none';
        });

        importExcelBtn.addEventListener('click', () => excelFileInput.click());
        excelFileInput.addEventListener('change', e => {
            const file = e.target.files[0];
            const reader = new FileReader();
            reader.onload = (event) => {
                const data = new Uint8Array(event.target.result);
                const workbook = XLSX.read(data, { type: 'array' });
                const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
                const jsonData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });
                
                jsonData.slice(1).forEach(row => {
                    if (row[0] && row[1] && row[2] && row[3] && row[4]) {
                        const newIndex = students.length + 1;
                        students.push({
                            name: row[0], level: row[1], group: row[2], fee: row[3], paid: false, phone: row[4],
                            picture: null, // No picture from Excel import
                            barcode: newIndex.toString().padStart(6, '0')
                        });
                        const groupExists = groups.some(g => g.level === row[1] && g.name === row[2]);
                        if (!groupExists) {
                            groups.push({ level: row[1], name: row[2] });
                            saveToStorage('sbea_groups', groups);
                        }
                    }
                });
                saveToStorage('sbea_students', students);
                renderStudents();
                alert(`${jsonData.length - 1} تلميذ تم استيرادهم بنجاح!`);
            };
            reader.readAsArrayBuffer(file);
            excelFileInput.value = '';
        });

        downloadTemplateBtn.addEventListener('click', () => {
            const headers = [
                ["الاسم الكامل", "المستوى", "الفوج", "المستحقات الشهرية", "هاتف ولي الأمر"]
            ];
            const ws = XLSX.utils.aoa_to_sheet(headers);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "نموذج");
            XLSX.writeFile(wb, "نموذج_تلاميذ.xlsx");
        });

        searchNameInput.addEventListener('input', renderStudents);
        searchLevelInput.addEventListener('change', renderStudents);
        searchBarcodeInput.addEventListener('input', renderStudents);

        printStudentsBtn.addEventListener('click', () => window.print());

        populateLevelsDropdown(studentLevelSelect);
        renderStudents();

        // إضافة دوال النوافذ المنبثقة
        initModals();
    }

    // =================================================================
    //                      Modal Functions
    // =================================================================
    function initModals() {
        // إغلاق النوافذ المنبثقة
        document.querySelectorAll('.close-modal, .cancel-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.modal').forEach(modal => {
                    modal.style.display = 'none';
                });
            });
        });

        // إغلاق النافذة عند النقر خارجها
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });
    }

    function openPaymentsModal(student) {
        const modal = document.getElementById('payments-modal');
        const studentInfo = document.getElementById('student-info');
        const paymentsGrid = document.getElementById('payments-grid');

        // حساب إجمالي المدفوع والباقي
        let totalPaid = 0;
        let totalRemaining = 0;
        let paidMonths = 0;
        let partialMonths = 0;
        let unpaidMonths = 0;

        if (student.monthlyPayments) {
            Object.values(student.monthlyPayments).forEach(payment => {
                totalPaid += payment.amount || 0;
                totalRemaining += payment.remaining || 0;
                if (payment.status === 'مدفوع') paidMonths++;
                else if (payment.status === 'جزئي') partialMonths++;
                else unpaidMonths++;
            });
        }

        // عرض معلومات التلميذ المحسنة
        studentInfo.innerHTML = `
            <div class="student-summary enhanced">
                <div class="student-header">
                    <img src="${student.picture || `${student.barcode.padStart(6, '0')}.jpg`}" alt="صورة ${student.name}" class="student-modal-image" onerror="this.src='logo.jpg'">
                    <div class="student-info">
                        <h4>${student.name}</h4>
                        <p><strong>المستوى:</strong> ${student.level} | <strong>الفوج:</strong> ${student.group}</p>
                        <p><strong>الرقم التسلسلي:</strong> ${student.barcode.padStart(6, '0')}</p>
                    </div>
                </div>
                <div class="payment-summary">
                    <div class="summary-row">
                        <div class="summary-item">
                            <span class="label">المستحقات الشهرية:</span>
                            <span class="value">${student.fee} DHS</span>
                        </div>
                        ${student.hasTransport ? `
                        <div class="summary-item">
                            <span class="label">رسوم النقل الشهرية:</span>
                            <span class="value">${student.transportFee} DHS</span>
                        </div>` : ''}
                    </div>
                    <div class="summary-row">
                        <div class="summary-item success">
                            <span class="label">إجمالي المدفوع:</span>
                            <span class="value">${totalPaid.toLocaleString()} DHS</span>
                        </div>
                        <div class="summary-item ${totalRemaining > 0 ? 'danger' : 'success'}">
                            <span class="label">إجمالي الباقي:</span>
                            <span class="value">${totalRemaining.toLocaleString()} DHS</span>
                        </div>
                    </div>
                    <div class="months-summary">
                        <div class="month-stat paid">
                            <span class="count">${paidMonths}</span>
                            <span class="label">أشهر مدفوعة</span>
                        </div>
                        <div class="month-stat partial">
                            <span class="count">${partialMonths}</span>
                            <span class="label">أشهر جزئية</span>
                        </div>
                        <div class="month-stat unpaid">
                            <span class="count">${unpaidMonths}</span>
                            <span class="label">أشهر غير مدفوعة</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إعداد التبويبات
        setupPaymentTabs(student);

        // عرض شبكة الدفعات الشهرية
        displayMonthlyPayments(student, paymentsGrid);

        // عرض الواجبات الإضافية
        displayAdditionalFees(student);

        modal.style.display = 'block';
    }

    function setupPaymentTabs(student) {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                // إزالة الفئة النشطة من جميع التبويبات
                tabBtns.forEach(b => b.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));

                // إضافة الفئة النشطة للتبويب المحدد
                btn.classList.add('active');
                const tabId = btn.dataset.tab;
                document.getElementById(`${tabId}-payments`).classList.add('active');
            });
        });

        // إعداد نموذج إضافة الواجبات الإضافية
        const addFeeForm = document.getElementById('add-additional-fee-form');
        addFeeForm.onsubmit = (e) => {
            e.preventDefault();
            addAdditionalFee(student);
        };
    }

    function displayMonthlyPayments(student, paymentsGrid) {
        let paymentsHTML = `
            <div class="payments-header">
                <h4>📅 الدفعات الشهرية للموسم الدراسي (10 أشهر)</h4>
                <p>من شهر سبتمبر إلى شهر يونيو - المستحقات الشهرية: ${student.fee} DHS</p>
            </div>
            <div class="payments-table">
                <table>
                    <thead>
                        <tr>
                            <th>📅 الشهر</th>
                            <th>💰 المبلغ المستحق</th>
                            <th>✅ المبلغ المدفوع</th>
                            <th>⚠️ الباقي</th>
                            <th>📊 الحالة</th>
                            <th>🔧 الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        ACADEMIC_MONTHS.forEach((month, index) => {
            const payment = student.monthlyPayments[month] || {
                status: 'غير مدفوع',
                amount: 0,
                remaining: parseFloat(student.fee),
                dueAmount: parseFloat(student.fee)
            };

            const monthNumber = index + 1;

            paymentsHTML += `
                <tr class="payment-row-${payment.status.replace(' ', '-')}">
                    <td class="month-cell">
                        <div class="month-info">
                            <span class="month-name">${month}</span>
                            <span class="month-number">الشهر ${monthNumber}</span>
                        </div>
                    </td>
                    <td class="amount-cell">${payment.dueAmount.toLocaleString()} DHS</td>
                    <td class="paid-cell">${payment.amount.toLocaleString()} DHS</td>
                    <td class="remaining-cell ${payment.remaining > 0 ? 'has-remaining' : 'no-remaining'}">
                        ${payment.remaining.toLocaleString()} DHS
                    </td>
                    <td class="status-cell">
                        <span class="status-badge status-${payment.status.replace(' ', '-')}">${payment.status}</span>
                    </td>
                    <td class="actions-cell">
                        ${payment.status !== 'مدفوع' ? `
                            <button class="pay-btn" data-month="${month}" data-barcode="${student.barcode}" title="دفع كامل">
                                💰 دفع كامل
                            </button>
                            <button class="partial-pay-btn" data-month="${month}" data-barcode="${student.barcode}" title="دفع جزئي">
                                📝 دفع جزئي
                            </button>
                        ` : `
                            <span class="paid-indicator">✅ مدفوع</span>
                        `}
                    </td>
                </tr>
            `;
        });

        paymentsHTML += '</tbody></table></div>';
        paymentsGrid.innerHTML = paymentsHTML;

        // إضافة معالجات الأحداث لأزرار الدفع
        paymentsGrid.querySelectorAll('.pay-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const month = e.target.dataset.month;
                const barcode = e.target.dataset.barcode;
                processPayment(barcode, month, 'full');
            });
        });

        paymentsGrid.querySelectorAll('.partial-pay-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const month = e.target.dataset.month;
                const barcode = e.target.dataset.barcode;
                const amount = prompt('أدخل المبلغ المدفوع:');
                if (amount && !isNaN(amount) && parseFloat(amount) > 0) {
                    processPayment(barcode, month, 'partial', parseFloat(amount));
                }
            });
        });
    }

    function displayAdditionalFees(student) {
        const additionalFeesList = document.getElementById('additional-fees-list');

        if (!student.additionalFees || student.additionalFees.length === 0) {
            additionalFeesList.innerHTML = '<p class="no-additional-fees">لا توجد واجبات إضافية</p>';
            return;
        }

        let html = '<div class="additional-fees-table"><table><thead><tr><th>الوصف</th><th>المبلغ</th><th>تاريخ الاستحقاق</th><th>الحالة</th><th>الإجراءات</th></tr></thead><tbody>';

        student.additionalFees.forEach((fee, index) => {
            const isOverdue = new Date(fee.dueDate) < new Date() && fee.status !== 'مدفوع';
            html += `
                <tr class="${isOverdue ? 'overdue' : ''}">
                    <td>${fee.description}</td>
                    <td>${fee.amount.toLocaleString()} DHS</td>
                    <td>${new Date(fee.dueDate).toLocaleDateString('ar-DZ')}</td>
                    <td class="status-${fee.status.replace(' ', '-')}">${fee.status}</td>
                    <td>
                        ${fee.status !== 'مدفوع' ? `
                            <button class="pay-additional-btn" data-index="${index}" data-barcode="${student.barcode}">دفع</button>
                            <button class="delete-additional-btn" data-index="${index}" data-barcode="${student.barcode}">حذف</button>
                        ` : ''}
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        additionalFeesList.innerHTML = html;

        // إضافة معالجات الأحداث
        additionalFeesList.querySelectorAll('.pay-additional-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const index = parseInt(e.target.dataset.index);
                const barcode = e.target.dataset.barcode;
                payAdditionalFee(barcode, index);
            });
        });

        additionalFeesList.querySelectorAll('.delete-additional-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const index = parseInt(e.target.dataset.index);
                const barcode = e.target.dataset.barcode;
                deleteAdditionalFee(barcode, index);
            });
        });
    }

    function addAdditionalFee(student) {
        const description = document.getElementById('fee-description').value.trim();
        const amount = parseFloat(document.getElementById('fee-amount').value);
        const dueDate = document.getElementById('fee-due-date').value;

        if (!description || !amount || !dueDate) {
            alert('يرجى ملء جميع الحقول');
            return;
        }

        const newFee = {
            description: description,
            amount: amount,
            dueDate: dueDate,
            status: 'غير مدفوع',
            createdDate: new Date().toISOString()
        };

        if (!student.additionalFees) {
            student.additionalFees = [];
        }

        student.additionalFees.push(newFee);

        // تحديث البيانات في التخزين
        const studentIndex = students.findIndex(s => s.barcode === student.barcode);
        if (studentIndex !== -1) {
            students[studentIndex] = student;
            saveToStorage('sbea_students', students);
        }

        // إعادة عرض الواجبات الإضافية
        displayAdditionalFees(student);

        // مسح النموذج
        document.getElementById('add-additional-fee-form').reset();

        alert('تم إضافة الواجب الإضافي بنجاح');
    }

    function payAdditionalFee(barcode, feeIndex) {
        const studentIndex = students.findIndex(s => s.barcode === barcode);
        if (studentIndex === -1) return;

        students[studentIndex].additionalFees[feeIndex].status = 'مدفوع';
        students[studentIndex].additionalFees[feeIndex].paidDate = new Date().toISOString();

        saveToStorage('sbea_students', students);
        displayAdditionalFees(students[studentIndex]);
        updateDashboardStats();

        alert('تم دفع الواجب الإضافي بنجاح');
    }

    function deleteAdditionalFee(barcode, feeIndex) {
        if (!confirm('هل أنت متأكد من حذف هذا الواجب الإضافي؟')) return;

        const studentIndex = students.findIndex(s => s.barcode === barcode);
        if (studentIndex === -1) return;

        students[studentIndex].additionalFees.splice(feeIndex, 1);

        saveToStorage('sbea_students', students);
        displayAdditionalFees(students[studentIndex]);

        alert('تم حذف الواجب الإضافي بنجاح');
    }

    function processPayment(barcode, month, type, amount = null) {
        const studentIndex = students.findIndex(s => s.barcode === barcode);
        if (studentIndex === -1) return;

        const student = students[studentIndex];
        const payment = student.monthlyPayments[month];
        const dueAmount = payment.dueAmount;

        if (type === 'full') {
            payment.amount = dueAmount;
            payment.remaining = 0;
            payment.status = 'مدفوع';
        } else if (type === 'partial' && amount) {
            const newAmount = Math.min(payment.amount + amount, dueAmount);
            payment.amount = newAmount;
            payment.remaining = dueAmount - newAmount;
            payment.status = payment.remaining > 0 ? 'جزئي' : 'مدفوع';
        }

        saveToStorage('sbea_students', students);
        openPaymentsModal(student); // إعادة فتح النافذة لتحديث البيانات
        renderStudents(); // تحديث الجدول الرئيسي
    }

    function openEditModal(student) {
        const modal = document.getElementById('edit-modal');
        const form = document.getElementById('edit-student-form');

        // ملء النموذج بالبيانات الحالية
        document.getElementById('edit-student-name').value = student.name;
        document.getElementById('edit-student-fee').value = student.fee;
        document.getElementById('edit-student-phone').value = student.phone;
        document.getElementById('edit-student-transport').checked = student.hasTransport;
        document.getElementById('edit-student-transport-fee').value = student.transportFee || '';

        // إظهار/إخفاء حقل رسوم النقل
        const editTransportFeeContainer = document.getElementById('edit-transport-fee-container');
        editTransportFeeContainer.style.display = student.hasTransport ? 'block' : 'none';

        // ملء قوائم المستويات والأفواج
        const editLevelSelect = document.getElementById('edit-student-level');
        const editGroupSelect = document.getElementById('edit-student-group');

        editLevelSelect.innerHTML = '<option value="">اختر المستوى</option>';
        ACADEMIC_LEVELS.forEach(level => {
            const option = document.createElement('option');
            option.value = level;
            option.textContent = level;
            if (level === student.level) option.selected = true;
            editLevelSelect.appendChild(option);
        });

        // تحديث الأفواج عند تغيير المستوى
        function updateEditGroupsDropdown(selectedLevel) {
            editGroupSelect.innerHTML = '<option value="">اختر الفوج</option>';
            const filteredGroups = groups.filter(g => g.level === selectedLevel);
            filteredGroups.forEach(group => {
                const option = document.createElement('option');
                option.value = group.name;
                option.textContent = group.name;
                if (group.name === student.group) option.selected = true;
                editGroupSelect.appendChild(option);
            });
        }

        updateEditGroupsDropdown(student.level);

        editLevelSelect.addEventListener('change', () => {
            updateEditGroupsDropdown(editLevelSelect.value);
        });

        // معالج تغيير النقل
        document.getElementById('edit-student-transport').addEventListener('change', (e) => {
            editTransportFeeContainer.style.display = e.target.checked ? 'block' : 'none';
        });

        // عرض الصورة الحالية
        const currentPicturePreview = document.getElementById('current-picture-preview');
        if (student.picture) {
            currentPicturePreview.innerHTML = `<img src="${student.picture}" alt="الصورة الحالية" style="max-width: 100px; max-height: 100px;">`;
        } else {
            currentPicturePreview.innerHTML = '<p>لا توجد صورة حالية</p>';
        }

        // معالج إرسال النموذج
        form.onsubmit = async (e) => {
            e.preventDefault();
            await updateStudent(student.barcode);
        };

        modal.style.display = 'block';
    }

    async function updateStudent(barcode) {
        const studentIndex = students.findIndex(s => s.barcode === barcode);
        if (studentIndex === -1) return;

        const pictureInput = document.getElementById('edit-student-picture');
        let pictureDataUrl = students[studentIndex].picture; // الاحتفاظ بالصورة الحالية

        if (pictureInput.files && pictureInput.files[0]) {
            try {
                pictureDataUrl = await new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result);
                    reader.onerror = reject;
                    reader.readAsDataURL(pictureInput.files[0]);
                });
            } catch (error) {
                console.error("Error reading file:", error);
                alert("حدث خطأ أثناء تحميل الصورة.");
                return;
            }
        }

        const oldFee = parseFloat(students[studentIndex].fee);
        const newFee = parseFloat(document.getElementById('edit-student-fee').value);

        // تحديث بيانات التلميذ
        students[studentIndex].name = document.getElementById('edit-student-name').value.trim();
        students[studentIndex].level = document.getElementById('edit-student-level').value;
        students[studentIndex].group = document.getElementById('edit-student-group').value;
        students[studentIndex].fee = newFee.toString();
        students[studentIndex].phone = document.getElementById('edit-student-phone').value.trim();
        students[studentIndex].hasTransport = document.getElementById('edit-student-transport').checked;
        students[studentIndex].transportFee = students[studentIndex].hasTransport ?
            document.getElementById('edit-student-transport-fee').value : '0';
        students[studentIndex].picture = pictureDataUrl;

        // تحديث المستحقات الشهرية إذا تغيرت الرسوم
        if (oldFee !== newFee) {
            Object.keys(students[studentIndex].monthlyPayments).forEach(month => {
                const payment = students[studentIndex].monthlyPayments[month];
                if (payment.status === 'غير مدفوع') {
                    payment.dueAmount = newFee;
                    payment.remaining = newFee;
                }
            });
        }

        saveToStorage('sbea_students', students);
        renderStudents();
        document.getElementById('edit-modal').style.display = 'none';
        alert('تم تحديث بيانات التلميذ بنجاح!');
    }

    // =================================================================
    //                      Teachers Page
    // =================================================================
    function initTeachersPage() {
        const teacherForm = document.getElementById('add-teacher-form');
        const teacherTableBody = document.querySelector('#teacher-table tbody');
        const teacherNameInput = document.getElementById('teacher-name');
        const teacherSalaryInput = document.getElementById('teacher-salary');
        const teacherTasksInput = document.getElementById('teacher-tasks');
        const levelsChecklist = document.getElementById('teacher-levels-checklist');
        const groupsChecklist = document.getElementById('teacher-groups-checklist');
        const subjectsChecklist = document.getElementById('teacher-subjects-checklist');

        function populateChecklist(container, items, name) {
            // الاحتفاظ بالعنوان الموجود وإضافة الخيارات فقط
            const existingTitle = container.querySelector('h4');
            const titleText = existingTitle ? existingTitle.textContent : name;

            container.innerHTML = `<h4>${titleText}</h4>`;

            if (items.length === 0) {
                container.innerHTML += '<p class="no-items">لا توجد عناصر متاحة. يرجى إضافتها أولاً.</p>';
                return;
            }

            const checklistContainer = document.createElement('div');
            checklistContainer.className = 'checkbox-list';

            items.forEach(item => {
                const checkboxId = `${name}-${item.replace(/\s+/g, '-').replace(/[^\w\-]/g, '')}`;
                const div = document.createElement('div');
                div.className = 'checkbox-item';
                div.innerHTML = `
                    <input type="checkbox" id="${checkboxId}" name="${name}" value="${item}">
                    <label for="${checkboxId}">${item}</label>
                `;
                checklistContainer.appendChild(div);
            });

            container.appendChild(checklistContainer);
        }

        function getUniqueSubjects() {
            // استخدام القائمة الشاملة للمواد
            return ALL_SUBJECTS;
        }

        function getUniqueGroups() {
            // إزالة التكرار من أسماء الأفواج
            const uniqueGroups = [...new Set(groups.map(g => g.name))];
            return uniqueGroups;
        }

        function renderTeachers() {
            teacherTableBody.innerHTML = '';
            teachers.forEach((teacher, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${teacher.name}</td>
                    <td>${teacher.salary || 'N/A'}</td>
                    <td>${teacher.levels.join(', ')}</td>
                    <td>${teacher.groups.join(', ')}</td>
                    <td>${teacher.subjects.join(', ')}</td>
                    <td>${teacher.tasks || 'N/A'}</td>
                    <td><button class="delete-btn">حذف</button></td>
                `;
                row.querySelector('.delete-btn').addEventListener('click', () => {
                    teachers.splice(index, 1);
                    saveToStorage('sbea_teachers', teachers);
                    renderTeachers();
                });
                teacherTableBody.appendChild(row);
            });
        }

        teacherForm.addEventListener('submit', e => {
            e.preventDefault();
            
            const getSelected = (container) => {
                return Array.from(container.querySelectorAll('input[type="checkbox"]:checked')).map(cb => cb.value);
            };

            const monthlySalary = parseFloat(teacherSalaryInput.value.trim());
            const newTeacher = {
                name: teacherNameInput.value.trim(),
                salary: monthlySalary.toString(),
                tasks: teacherTasksInput.value.trim(),
                levels: getSelected(levelsChecklist),
                groups: getSelected(groupsChecklist),
                subjects: getSelected(subjectsChecklist),
                monthlySalaries: createMonthlySalaries(monthlySalary)
            };

            teachers.push(newTeacher);
            saveToStorage('sbea_teachers', teachers);
            renderTeachers();
            teacherForm.reset();
            // Also uncheck all checkboxes
            Array.from(teacherForm.querySelectorAll('input[type="checkbox"]')).forEach(cb => cb.checked = false);
        });

        // التأكد من وجود بيانات الأفواج
        if (groups.length === 0) {
            groups = [
                { level: "الثالث ابتدائي", name: "فوج أ" },
                { level: "الثالث ابتدائي", name: "فوج ب" },
                { level: "الخامس ابتدائي", name: "فوج أ" },
                { level: "الخامس ابتدائي", name: "فوج ب" },
                { level: "الأولى إعدادي", name: "فوج أ" },
                { level: "الأولى إعدادي", name: "فوج ب" }
            ];
            saveToStorage('sbea_groups', groups);
        }

        // تحديث الخيارات في النموذج
        populateTeacherOptions();
        renderTeachers();
    }

    function populateTeacherOptions() {
        const levelsChecklist = document.getElementById('teacher-levels-checklist');
        const groupsChecklist = document.getElementById('teacher-groups-checklist');
        const subjectsChecklist = document.getElementById('teacher-subjects-checklist');

        // دالة مساعدة للحصول على الأفواج الفريدة
        function getUniqueGroupsLocal() {
            const uniqueGroups = [...new Set(groups.map(g => g.name))];
            return uniqueGroups;
        }

        // دالة مساعدة للحصول على المواد
        function getUniqueSubjectsLocal() {
            return ALL_SUBJECTS;
        }

        if (levelsChecklist) {
            levelsChecklist.innerHTML = '';
            const checkboxList = document.createElement('div');
            checkboxList.className = 'checkbox-list';

            ACADEMIC_LEVELS.forEach(level => {
                const checkboxId = `level-${level.replace(/\s+/g, '-').replace(/[^\w\-]/g, '')}`;
                const div = document.createElement('div');
                div.className = 'checkbox-item';
                div.innerHTML = `
                    <input type="checkbox" id="${checkboxId}" name="levels" value="${level}">
                    <label for="${checkboxId}">${level}</label>
                `;
                checkboxList.appendChild(div);
            });

            levelsChecklist.appendChild(checkboxList);
        }

        if (groupsChecklist) {
            groupsChecklist.innerHTML = '';
            const checkboxList = document.createElement('div');
            checkboxList.className = 'checkbox-list';

            const uniqueGroups = getUniqueGroupsLocal();
            uniqueGroups.forEach(group => {
                const checkboxId = `group-${group.replace(/\s+/g, '-').replace(/[^\w\-]/g, '')}`;
                const div = document.createElement('div');
                div.className = 'checkbox-item';
                div.innerHTML = `
                    <input type="checkbox" id="${checkboxId}" name="groups" value="${group}">
                    <label for="${checkboxId}">${group}</label>
                `;
                checkboxList.appendChild(div);
            });

            groupsChecklist.appendChild(checkboxList);
        }

        if (subjectsChecklist) {
            subjectsChecklist.innerHTML = '';
            const checkboxList = document.createElement('div');
            checkboxList.className = 'checkbox-list';

            const subjects = getUniqueSubjectsLocal();
            subjects.forEach(subject => {
                const checkboxId = `subject-${subject.replace(/\s+/g, '-').replace(/[^\w\-]/g, '')}`;
                const div = document.createElement('div');
                div.className = 'checkbox-item';
                div.innerHTML = `
                    <input type="checkbox" id="${checkboxId}" name="subjects" value="${subject}">
                    <label for="${checkboxId}">${subject}</label>
                `;
                checkboxList.appendChild(div);
            });

            subjectsChecklist.appendChild(checkboxList);
        }
    }

    // =================================================================
    //                      Groups Page (Levels & Groups)
    // =================================================================
    function initGroupsPage() {
        const groupForm = document.getElementById('add-group-form');
        const levelSelect = document.getElementById('level-select');
        const groupNameInput = document.getElementById('group-name');
        const listContainer = document.getElementById('levels-and-groups-list');

        function populateLevelsDropdown() {
            ACADEMIC_LEVELS.forEach(level => {
                levelSelect.appendChild(new Option(level, level));
            });
        }

        function getSubjectsForLevel(level) {
            if (level === "الأول ابتدائي") return SUBJECTS_BY_LEVEL["الأول ابتدائي"];
            if (level.includes("ابتدائي") && level !== "الأول ابتدائي") return SUBJECTS_BY_LEVEL["الابتدائي (2-6)"];
            if (level.includes("إعدادي")) return SUBJECTS_BY_LEVEL["الإعدادي"];
            return null;
        }

        function renderLevelsAndGroups() {
            listContainer.innerHTML = '';
            ACADEMIC_LEVELS.forEach(level => {
                const levelContainer = document.createElement('div');
                levelContainer.className = 'level-container';
                
                let subjectsHTML = '<h4>المواد الدراسية:</h4>';
                const subjects = getSubjectsForLevel(level);
                if (subjects) {
                    subjectsHTML += '<div class="subjects-grid">';
                    for (const category in subjects) {
                        subjectsHTML += `<div class="subject-category"><h5>${category}</h5><ul>`;
                        subjects[category].forEach(subject => {
                            subjectsHTML += `<li>${subject}</li>`;
                        });
                        subjectsHTML += '</ul></div>';
                    }
                    subjectsHTML += '</div>';
                } else {
                    subjectsHTML += '<p>لا توجد مواد محددة لهذا المستوى.</p>';
                }

                levelContainer.innerHTML = `<h3>${level}</h3>${subjectsHTML}`;

                const ul = document.createElement('ul');
                ul.innerHTML = '<h4>الأفواج:</h4>';
                const levelGroups = groups.filter(g => g.level === level);
                if (levelGroups.length > 0) {
                    levelGroups.forEach(group => {
                        const li = document.createElement('li');
                        li.textContent = `الفوج: ${group.name}`;
                        const deleteBtn = document.createElement('button');
                        deleteBtn.textContent = 'حذف';
                        deleteBtn.className = 'delete-btn';
                        deleteBtn.onclick = () => {
                            const groupIndex = groups.findIndex(g => g.level === level && g.name === group.name);
                            if (groupIndex > -1) {
                                groups.splice(groupIndex, 1);
                                saveToStorage('sbea_groups', groups);
                                renderLevelsAndGroups();
                            }
                        };
                        li.appendChild(deleteBtn);
                        ul.appendChild(li);
                    });
                } else {
                    const li = document.createElement('li');
                    li.textContent = 'لا توجد أفواج في هذا المستوى.';
                    ul.appendChild(li);
                }
                levelContainer.appendChild(ul);
                listContainer.appendChild(levelContainer);
            });
        }

        groupForm.addEventListener('submit', e => {
            e.preventDefault();
            const level = levelSelect.value;
            const name = groupNameInput.value.trim();
            if (level && name && !groups.some(g => g.level === level && g.name === name)) {
                groups.push({ level, name });
                saveToStorage('sbea_groups', groups);
                renderLevelsAndGroups();
                groupNameInput.value = '';
            } else {
                alert('هذا الفوج موجود بالفعل في هذا المستوى.');
            }
        });

        populateLevelsDropdown();
        renderLevelsAndGroups();
        initAdvancedManagement();
    }

    function initAdvancedManagement() {
        // إعداد التبويبات
        const mgmtTabBtns = document.querySelectorAll('.mgmt-tab-btn');
        const mgmtTabContents = document.querySelectorAll('.mgmt-tab-content');

        mgmtTabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                mgmtTabBtns.forEach(b => b.classList.remove('active'));
                mgmtTabContents.forEach(c => c.classList.remove('active'));

                btn.classList.add('active');
                const tabId = btn.dataset.tab;
                document.getElementById(`${tabId}-tab`).classList.add('active');
            });
        });

        // ملء القوائم المنسدلة
        populateAdvancedDropdowns();

        // إعداد النماذج
        setupAdvancedForms();

        // عرض البيانات الحالية
        displayCurriculum();
        displayInspections();
        displayAdminNotes();
        displayCompetitions();
    }

    function populateAdvancedDropdowns() {
        const levelSelects = [
            'curriculum-level', 'inspection-level', 'note-level', 'competition-level'
        ];

        levelSelects.forEach(selectId => {
            const select = document.getElementById(selectId);
            if (select) {
                select.innerHTML = '<option value="">اختر المستوى</option>';
                ACADEMIC_LEVELS.forEach(level => {
                    const option = document.createElement('option');
                    option.value = level;
                    option.textContent = level;
                    select.appendChild(option);
                });
            }
        });

        const subjectSelects = [
            'curriculum-subject', 'inspection-subject', 'competition-subject'
        ];

        subjectSelects.forEach(selectId => {
            const select = document.getElementById(selectId);
            if (select) {
                select.innerHTML = '<option value="">اختر المادة</option>';
                ALL_SUBJECTS.forEach(subject => {
                    const option = document.createElement('option');
                    option.value = subject;
                    option.textContent = subject;
                    select.appendChild(option);
                });
            }
        });

        // تحديث قائمة الأفواج عند تغيير المستوى
        const noteLevelSelect = document.getElementById('note-level');
        const noteGroupSelect = document.getElementById('note-group');

        if (noteLevelSelect && noteGroupSelect) {
            noteLevelSelect.addEventListener('change', () => {
                noteGroupSelect.innerHTML = '<option value="">اختر الفوج</option>';
                const filteredGroups = groups.filter(g => g.level === noteLevelSelect.value);
                filteredGroups.forEach(group => {
                    const option = document.createElement('option');
                    option.value = group.name;
                    option.textContent = group.name;
                    noteGroupSelect.appendChild(option);
                });
            });
        }
    }

    function setupAdvancedForms() {
        // نموذج المقررات الدراسية
        const curriculumForm = document.getElementById('add-curriculum-form');
        if (curriculumForm) {
            curriculumForm.addEventListener('submit', (e) => {
                e.preventDefault();
                addCurriculumItem();
            });
        }

        // نموذج زيارات المفتش
        const inspectionForm = document.getElementById('add-inspection-form');
        if (inspectionForm) {
            inspectionForm.addEventListener('submit', (e) => {
                e.preventDefault();
                addInspection();
            });
        }

        // نموذج الملاحظات الإدارية
        const noteForm = document.getElementById('add-note-form');
        if (noteForm) {
            noteForm.addEventListener('submit', (e) => {
                e.preventDefault();
                addAdminNote();
            });
        }

        // نموذج المسابقات التربوية
        const competitionForm = document.getElementById('add-competition-form');
        if (competitionForm) {
            competitionForm.addEventListener('submit', (e) => {
                e.preventDefault();
                addCompetition();
            });
        }
    }

    function addCurriculumItem() {
        const newItem = {
            id: Date.now().toString(),
            level: document.getElementById('curriculum-level').value,
            subject: document.getElementById('curriculum-subject').value,
            topic: document.getElementById('curriculum-topic').value,
            date: document.getElementById('curriculum-date').value,
            notes: document.getElementById('curriculum-notes').value,
            createdAt: new Date().toISOString()
        };

        curriculum.push(newItem);
        saveToStorage('sbea_curriculum', curriculum);
        displayCurriculum();
        document.getElementById('add-curriculum-form').reset();
        alert('تم إضافة المقرر بنجاح');
    }

    function addInspection() {
        const newInspection = {
            id: Date.now().toString(),
            inspectorName: document.getElementById('inspector-name').value,
            level: document.getElementById('inspection-level').value,
            subject: document.getElementById('inspection-subject').value,
            date: document.getElementById('inspection-date').value,
            time: document.getElementById('inspection-time').value,
            notes: document.getElementById('inspection-notes').value,
            rating: document.getElementById('inspection-rating').value,
            createdAt: new Date().toISOString()
        };

        inspections.push(newInspection);
        saveToStorage('sbea_inspections', inspections);
        displayInspections();
        document.getElementById('add-inspection-form').reset();
        alert('تم إضافة زيارة المفتش بنجاح');
    }

    function addAdminNote() {
        const newNote = {
            id: Date.now().toString(),
            level: document.getElementById('note-level').value,
            group: document.getElementById('note-group').value,
            type: document.getElementById('note-type').value,
            content: document.getElementById('note-content').value,
            date: document.getElementById('note-date').value,
            createdAt: new Date().toISOString()
        };

        adminNotes.push(newNote);
        saveToStorage('sbea_admin_notes', adminNotes);
        displayAdminNotes();
        document.getElementById('add-note-form').reset();
        alert('تم إضافة الملاحظة بنجاح');
    }

    function addCompetition() {
        const newCompetition = {
            id: Date.now().toString(),
            name: document.getElementById('competition-name').value,
            level: document.getElementById('competition-level').value,
            subject: document.getElementById('competition-subject').value,
            date: document.getElementById('competition-date').value,
            time: document.getElementById('competition-time').value,
            description: document.getElementById('competition-description').value,
            prize: document.getElementById('competition-prize').value,
            createdAt: new Date().toISOString()
        };

        competitions.push(newCompetition);
        saveToStorage('sbea_competitions', competitions);
        displayCompetitions();
        document.getElementById('add-competition-form').reset();
        alert('تم إضافة المسابقة بنجاح');
    }

    function displayCurriculum() {
        const curriculumList = document.getElementById('curriculum-list');
        if (!curriculumList) return;

        if (curriculum.length === 0) {
            curriculumList.innerHTML = '<p class="no-data">لا توجد مقررات دراسية مسجلة</p>';
            return;
        }

        let html = '<div class="data-table"><table><thead><tr><th>المستوى</th><th>المادة</th><th>الموضوع</th><th>التاريخ</th><th>الملاحظات</th><th>الإجراءات</th></tr></thead><tbody>';

        curriculum.forEach(item => {
            html += `
                <tr>
                    <td>${item.level}</td>
                    <td>${item.subject}</td>
                    <td>${item.topic}</td>
                    <td>${new Date(item.date).toLocaleDateString('ar-DZ')}</td>
                    <td>${item.notes || '-'}</td>
                    <td>
                        <button class="delete-btn" onclick="deleteCurriculumItem('${item.id}')">حذف</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        curriculumList.innerHTML = html;
    }

    function displayInspections() {
        const inspectionsList = document.getElementById('inspections-list');
        if (!inspectionsList) return;

        if (inspections.length === 0) {
            inspectionsList.innerHTML = '<p class="no-data">لا توجد زيارات مفتش مسجلة</p>';
            return;
        }

        let html = '<div class="data-table"><table><thead><tr><th>المفتش</th><th>المستوى</th><th>المادة</th><th>التاريخ</th><th>الوقت</th><th>التقييم</th><th>الملاحظات</th><th>الإجراءات</th></tr></thead><tbody>';

        inspections.forEach(inspection => {
            html += `
                <tr>
                    <td>${inspection.inspectorName}</td>
                    <td>${inspection.level}</td>
                    <td>${inspection.subject}</td>
                    <td>${new Date(inspection.date).toLocaleDateString('ar-DZ')}</td>
                    <td>${inspection.time}</td>
                    <td class="rating-${inspection.rating.replace(' ', '-')}">${inspection.rating}</td>
                    <td>${inspection.notes || '-'}</td>
                    <td>
                        <button class="delete-btn" onclick="deleteInspection('${inspection.id}')">حذف</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        inspectionsList.innerHTML = html;
    }

    function displayAdminNotes() {
        const notesList = document.getElementById('notes-list');
        if (!notesList) return;

        if (adminNotes.length === 0) {
            notesList.innerHTML = '<p class="no-data">لا توجد ملاحظات إدارية مسجلة</p>';
            return;
        }

        let html = '<div class="data-table"><table><thead><tr><th>المستوى</th><th>الفوج</th><th>النوع</th><th>المحتوى</th><th>التاريخ</th><th>الإجراءات</th></tr></thead><tbody>';

        adminNotes.forEach(note => {
            html += `
                <tr>
                    <td>${note.level}</td>
                    <td>${note.group}</td>
                    <td class="note-type-${note.type}">${note.type}</td>
                    <td>${note.content}</td>
                    <td>${new Date(note.date).toLocaleDateString('ar-DZ')}</td>
                    <td>
                        <button class="delete-btn" onclick="deleteAdminNote('${note.id}')">حذف</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        notesList.innerHTML = html;
    }

    function displayCompetitions() {
        const competitionsList = document.getElementById('competitions-list');
        if (!competitionsList) return;

        if (competitions.length === 0) {
            competitionsList.innerHTML = '<p class="no-data">لا توجد مسابقات تربوية مسجلة</p>';
            return;
        }

        let html = '<div class="data-table"><table><thead><tr><th>اسم المسابقة</th><th>المستوى</th><th>المادة</th><th>التاريخ</th><th>الوقت</th><th>الوصف</th><th>الجائزة</th><th>الإجراءات</th></tr></thead><tbody>';

        competitions.forEach(competition => {
            html += `
                <tr>
                    <td>${competition.name}</td>
                    <td>${competition.level}</td>
                    <td>${competition.subject}</td>
                    <td>${new Date(competition.date).toLocaleDateString('ar-DZ')}</td>
                    <td>${competition.time}</td>
                    <td>${competition.description || '-'}</td>
                    <td>${competition.prize || '-'}</td>
                    <td>
                        <button class="delete-btn" onclick="deleteCompetition('${competition.id}')">حذف</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        competitionsList.innerHTML = html;
    }

    // دوال الحذف
    window.deleteCurriculumItem = function(id) {
        if (confirm('هل أنت متأكد من حذف هذا المقرر؟')) {
            curriculum = curriculum.filter(item => item.id !== id);
            saveToStorage('sbea_curriculum', curriculum);
            displayCurriculum();
        }
    };

    window.deleteInspection = function(id) {
        if (confirm('هل أنت متأكد من حذف زيارة المفتش هذه؟')) {
            inspections = inspections.filter(item => item.id !== id);
            saveToStorage('sbea_inspections', inspections);
            displayInspections();
        }
    };

    window.deleteAdminNote = function(id) {
        if (confirm('هل أنت متأكد من حذف هذه الملاحظة؟')) {
            adminNotes = adminNotes.filter(item => item.id !== id);
            saveToStorage('sbea_admin_notes', adminNotes);
            displayAdminNotes();
        }
    };

    window.deleteCompetition = function(id) {
        if (confirm('هل أنت متأكد من حذف هذه المسابقة؟')) {
            competitions = competitions.filter(item => item.id !== id);
            saveToStorage('sbea_competitions', competitions);
            displayCompetitions();
        }
    };

    // =================================================================
    //                      Activities Page
    // =================================================================
    function initActivitiesPage() {
        const activityForm = document.getElementById('add-activity-form');
        const activitiesList = document.getElementById('activities-list');

        function renderActivities() {
            activitiesList.innerHTML = '';
            activities.forEach((activity, index) => {
                const card = document.createElement('div');
                card.className = 'activity-card';
                const mediaHTML = activity.media.map(src => {
                    if (src.startsWith('data:image')) {
                        return `<img src="${src}" alt="Activity media">`;
                    } else if (src.startsWith('data:video')) {
                        return `<video controls src="${src}"></video>`;
                    }
                    return '';
                }).join('');

                card.innerHTML = `
                    <h3>${activity.name}</h3>
                    <p><strong>الفئة المستهدفة:</strong> ${activity.target}</p>
                    <p><strong>المكان:</strong> ${activity.location}</p>
                    <p><strong>التوقيت:</strong> ${new Date(activity.datetime).toLocaleString('ar-DZ')}</p>
                    <div class="media-container">${mediaHTML}</div>
                    <button class="delete-btn">حذف النشاط</button>
                `;
                card.querySelector('.delete-btn').addEventListener('click', () => {
                    activities.splice(index, 1);
                    saveToStorage('sbea_activities', activities);
                    renderActivities();
                });
                activitiesList.appendChild(card);
            });
        }

        activityForm.addEventListener('submit', e => {
            e.preventDefault();
            const mediaInput = document.getElementById('activity-media');
            const mediaFiles = Array.from(mediaInput.files);
            const mediaPromises = mediaFiles.map(file => {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result);
                    reader.onerror = reject;
                    reader.readAsDataURL(file);
                });
            });

            Promise.all(mediaPromises).then(mediaDataUrls => {
                const newActivity = {
                    name: document.getElementById('activity-name').value,
                    target: document.getElementById('activity-target').value,
                    location: document.getElementById('activity-location').value,
                    datetime: document.getElementById('activity-datetime').value,
                    media: mediaDataUrls
                };
                activities.push(newActivity);
                saveToStorage('sbea_activities', activities);
                renderActivities();
                activityForm.reset();
            });
        });

        renderActivities();
    }

    // =================================================================
    //                      Settings Page
    // =================================================================
    function initSettingsPage() {
        const backupBtn = document.getElementById('backup-btn');
        const restoreInput = document.getElementById('restore-input');

        backupBtn.addEventListener('click', () => {
            const data = {
                students: getFromStorage('sbea_students'),
                groups: getFromStorage('sbea_groups'),
                teachers: getFromStorage('sbea_teachers'),
                activities: getFromStorage('sbea_activities')
            };
            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(data));
            const dlAnchorElem = document.createElement('a');
            dlAnchorElem.setAttribute("href", dataStr);
            dlAnchorElem.setAttribute("download", `sbea_backup_${new Date().toISOString().split('T')[0]}.json`);
            dlAnchorElem.click();
        });

        restoreInput.addEventListener('change', e => {
            const file = e.target.files[0];
            if (!file) return;
            const reader = new FileReader();
            reader.onload = (event) => {
                try {
                    const data = JSON.parse(event.target.result);
                    if (data.students && data.groups && data.teachers) {
                        saveToStorage('sbea_students', data.students);
                        saveToStorage('sbea_groups', data.groups);
                        saveToStorage('sbea_teachers', data.teachers);
                        saveToStorage('sbea_activities', data.activities || []);
                        alert('تم استعادة البيانات بنجاح! قم بتحديث الصفحة لرؤية التغييرات.');
                    } else {
                        alert('ملف النسخ الاحتياطي غير صالح.');
                    }
                } catch (error) {
                    alert('خطأ في قراءة الملف.');
                }
            };
            reader.readAsText(file);
            restoreInput.value = '';
        });
    }

    // =================================================================
    //                      Staff Page
    // =================================================================
    function initStaffPage() {
        // إعداد التبويبات
        const staffTabBtns = document.querySelectorAll('.staff-tab-btn');
        const staffTabContents = document.querySelectorAll('.staff-tab-content');

        staffTabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                staffTabBtns.forEach(b => b.classList.remove('active'));
                staffTabContents.forEach(c => c.classList.remove('active'));

                btn.classList.add('active');
                const tabId = btn.dataset.tab;
                document.getElementById(`${tabId}-tab`).classList.add('active');
            });
        });

        // إعداد النماذج
        setupStaffForms();

        // عرض البيانات
        displayAdminStaff();
        displayGuards();
        displayCleaners();
        displayTeacherSalaries();

        // إعداد النوافذ المنبثقة
        initStaffModals();
    }

    function setupStaffForms() {
        // نموذج الموظفين الإداريين
        const adminForm = document.getElementById('add-admin-form');
        if (adminForm) {
            adminForm.addEventListener('submit', (e) => {
                e.preventDefault();
                addAdminStaff();
            });
        }

        // نموذج الحراس
        const guardForm = document.getElementById('add-guard-form');
        if (guardForm) {
            guardForm.addEventListener('submit', (e) => {
                e.preventDefault();
                addGuard();
            });
        }

        // نموذج مسؤولي النظافة
        const cleanerForm = document.getElementById('add-cleaner-form');
        if (cleanerForm) {
            cleanerForm.addEventListener('submit', (e) => {
                e.preventDefault();
                addCleaner();
            });
        }
    }

    function addAdminStaff() {
        const newStaff = {
            id: Date.now().toString(),
            name: document.getElementById('admin-name').value.trim(),
            position: document.getElementById('admin-position').value.trim(),
            salary: parseFloat(document.getElementById('admin-salary').value),
            phone: document.getElementById('admin-phone').value.trim(),
            email: document.getElementById('admin-email').value.trim(),
            hireDate: document.getElementById('admin-hire-date').value,
            monthlySalaries: createMonthlySalaries(parseFloat(document.getElementById('admin-salary').value)),
            createdAt: new Date().toISOString()
        };

        adminStaff.push(newStaff);
        saveToStorage('sbea_admin_staff', adminStaff);
        displayAdminStaff();
        document.getElementById('add-admin-form').reset();
        alert('تم إضافة الموظف الإداري بنجاح');
    }

    function addGuard() {
        const newGuard = {
            id: Date.now().toString(),
            name: document.getElementById('guard-name').value.trim(),
            shift: document.getElementById('guard-shift').value,
            salary: parseFloat(document.getElementById('guard-salary').value),
            phone: document.getElementById('guard-phone').value.trim(),
            hireDate: document.getElementById('guard-hire-date').value,
            monthlySalaries: createMonthlySalaries(parseFloat(document.getElementById('guard-salary').value)),
            createdAt: new Date().toISOString()
        };

        guards.push(newGuard);
        saveToStorage('sbea_guards', guards);
        displayGuards();
        document.getElementById('add-guard-form').reset();
        alert('تم إضافة الحارس بنجاح');
    }

    function addCleaner() {
        const newCleaner = {
            id: Date.now().toString(),
            name: document.getElementById('cleaner-name').value.trim(),
            area: document.getElementById('cleaner-area').value,
            salary: parseFloat(document.getElementById('cleaner-salary').value),
            phone: document.getElementById('cleaner-phone').value.trim(),
            hireDate: document.getElementById('cleaner-hire-date').value,
            monthlySalaries: createMonthlySalaries(parseFloat(document.getElementById('cleaner-salary').value)),
            createdAt: new Date().toISOString()
        };

        cleaners.push(newCleaner);
        saveToStorage('sbea_cleaners', cleaners);
        displayCleaners();
        document.getElementById('add-cleaner-form').reset();
        alert('تم إضافة مسؤول النظافة بنجاح');
    }

    function createMonthlySalaries(monthlySalary) {
        const salaries = {};
        ACADEMIC_MONTHS.forEach(month => {
            salaries[month] = {
                status: 'غير مدفوع',
                amount: 0,
                remaining: monthlySalary,
                dueAmount: monthlySalary
            };
        });
        return salaries;
    }

    function displayAdminStaff() {
        const adminList = document.getElementById('admin-list');
        if (!adminList) return;

        if (adminStaff.length === 0) {
            adminList.innerHTML = '<p class="no-data">لا يوجد موظفون إداريون مسجلون</p>';
            return;
        }

        let html = '<div class="staff-table"><table><thead><tr><th>الاسم</th><th>المنصب</th><th>الراتب الشهري</th><th>الهاتف</th><th>البريد الإلكتروني</th><th>تاريخ التوظيف</th><th>الإجراءات</th></tr></thead><tbody>';

        adminStaff.forEach(staff => {
            html += `
                <tr>
                    <td>${staff.name}</td>
                    <td>${staff.position}</td>
                    <td>${staff.salary.toLocaleString()} DHS</td>
                    <td>${staff.phone}</td>
                    <td>${staff.email || '-'}</td>
                    <td>${new Date(staff.hireDate).toLocaleDateString('ar-DZ')}</td>
                    <td>
                        <button class="salary-btn" onclick="openSalaryModal('${staff.id}', 'admin')">الراتب</button>
                        <button class="delete-btn" onclick="deleteAdminStaff('${staff.id}')">حذف</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        adminList.innerHTML = html;
    }

    function displayGuards() {
        const guardsList = document.getElementById('guards-list');
        if (!guardsList) return;

        if (guards.length === 0) {
            guardsList.innerHTML = '<p class="no-data">لا يوجد حراس مسجلون</p>';
            return;
        }

        let html = '<div class="staff-table"><table><thead><tr><th>الاسم</th><th>الوردية</th><th>الراتب الشهري</th><th>الهاتف</th><th>تاريخ التوظيف</th><th>الإجراءات</th></tr></thead><tbody>';

        guards.forEach(guard => {
            html += `
                <tr>
                    <td>${guard.name}</td>
                    <td>${guard.shift}</td>
                    <td>${guard.salary.toLocaleString()} DHS</td>
                    <td>${guard.phone}</td>
                    <td>${new Date(guard.hireDate).toLocaleDateString('ar-DZ')}</td>
                    <td>
                        <button class="salary-btn" onclick="openSalaryModal('${guard.id}', 'guard')">الراتب</button>
                        <button class="delete-btn" onclick="deleteGuard('${guard.id}')">حذف</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        guardsList.innerHTML = html;
    }

    function displayCleaners() {
        const cleanersList = document.getElementById('cleaners-list');
        if (!cleanersList) return;

        if (cleaners.length === 0) {
            cleanersList.innerHTML = '<p class="no-data">لا يوجد مسؤولو نظافة مسجلون</p>';
            return;
        }

        let html = '<div class="staff-table"><table><thead><tr><th>الاسم</th><th>المنطقة المسؤول عنها</th><th>الراتب الشهري</th><th>الهاتف</th><th>تاريخ التوظيف</th><th>الإجراءات</th></tr></thead><tbody>';

        cleaners.forEach(cleaner => {
            html += `
                <tr>
                    <td>${cleaner.name}</td>
                    <td>${cleaner.area}</td>
                    <td>${cleaner.salary.toLocaleString()} DHS</td>
                    <td>${cleaner.phone}</td>
                    <td>${new Date(cleaner.hireDate).toLocaleDateString('ar-DZ')}</td>
                    <td>
                        <button class="salary-btn" onclick="openSalaryModal('${cleaner.id}', 'cleaner')">الراتب</button>
                        <button class="delete-btn" onclick="deleteCleaner('${cleaner.id}')">حذف</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        cleanersList.innerHTML = html;
    }

    function displayTeacherSalaries() {
        const teacherSalariesList = document.getElementById('teacher-salaries-list');
        if (!teacherSalariesList) return;

        // حساب إجمالي الرواتب
        let totalSalaries = 0;
        let paidSalaries = 0;
        let remainingSalaries = 0;

        teachers.forEach(teacher => {
            const monthlySalary = parseFloat(teacher.salary || 0);
            totalSalaries += monthlySalary * 10; // 10 أشهر

            if (teacher.monthlySalaries) {
                Object.values(teacher.monthlySalaries).forEach(salary => {
                    paidSalaries += salary.amount || 0;
                    remainingSalaries += salary.remaining || 0;
                });
            } else {
                remainingSalaries += monthlySalary * 10;
            }
        });

        // تحديث الملخص
        document.getElementById('total-teacher-salaries').textContent = totalSalaries.toLocaleString() + ' DHS';
        document.getElementById('paid-teacher-salaries').textContent = paidSalaries.toLocaleString() + ' DHS';
        document.getElementById('remaining-teacher-salaries').textContent = remainingSalaries.toLocaleString() + ' DHS';

        if (teachers.length === 0) {
            teacherSalariesList.innerHTML = '<p class="no-data">لا يوجد أساتذة مسجلون</p>';
            return;
        }

        let html = '<div class="staff-table"><table><thead><tr><th>اسم الأستاذ</th><th>الراتب الشهري</th><th>إجمالي المدفوع</th><th>إجمالي الباقي</th><th>الإجراءات</th></tr></thead><tbody>';

        teachers.forEach(teacher => {
            let teacherPaid = 0;
            let teacherRemaining = 0;

            if (teacher.monthlySalaries) {
                Object.values(teacher.monthlySalaries).forEach(salary => {
                    teacherPaid += salary.amount || 0;
                    teacherRemaining += salary.remaining || 0;
                });
            } else {
                teacherRemaining = parseFloat(teacher.salary || 0) * 10;
            }

            html += `
                <tr>
                    <td>${teacher.name}</td>
                    <td>${parseFloat(teacher.salary || 0).toLocaleString()} DHS</td>
                    <td class="paid">${teacherPaid.toLocaleString()} DHS</td>
                    <td class="${teacherRemaining > 0 ? 'unpaid' : 'paid'}">${teacherRemaining.toLocaleString()} DHS</td>
                    <td>
                        <button class="salary-btn" onclick="openTeacherSalaryModal('${teacher.name}')">إدارة الراتب</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        teacherSalariesList.innerHTML = html;
    }

    function initStaffModals() {
        // إغلاق النوافذ المنبثقة
        document.querySelectorAll('.close-modal').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.modal').forEach(modal => {
                    modal.style.display = 'none';
                });
            });
        });
    }

    // دوال الحذف
    window.deleteAdminStaff = function(id) {
        if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
            adminStaff = adminStaff.filter(staff => staff.id !== id);
            saveToStorage('sbea_admin_staff', adminStaff);
            displayAdminStaff();
        }
    };

    window.deleteGuard = function(id) {
        if (confirm('هل أنت متأكد من حذف هذا الحارس؟')) {
            guards = guards.filter(guard => guard.id !== id);
            saveToStorage('sbea_guards', guards);
            displayGuards();
        }
    };

    window.deleteCleaner = function(id) {
        if (confirm('هل أنت متأكد من حذف مسؤول النظافة هذا؟')) {
            cleaners = cleaners.filter(cleaner => cleaner.id !== id);
            saveToStorage('sbea_cleaners', cleaners);
            displayCleaners();
        }
    };

    window.openSalaryModal = function(id, type) {
        // سيتم تطوير هذه الوظيفة لاحقاً
        alert('سيتم تفعيل إدارة الرواتب قريباً');
    };

    window.openTeacherSalaryModal = function(teacherName) {
        // سيتم تطوير هذه الوظيفة لاحقاً
        alert('سيتم تفعيل إدارة رواتب الأساتذة قريباً');
    };

    // =================================================================
    //                      Financial Page
    // =================================================================
    function initFinancialPage() {
        // إعداد التبويبات
        const financialTabBtns = document.querySelectorAll('.financial-tab-btn');
        const financialTabContents = document.querySelectorAll('.financial-tab-content');

        financialTabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                financialTabBtns.forEach(b => b.classList.remove('active'));
                financialTabContents.forEach(c => c.classList.remove('active'));

                btn.classList.add('active');
                const tabId = btn.dataset.tab;
                document.getElementById(`${tabId}-tab`).classList.add('active');
            });
        });

        // إعداد البحث والفلاتر
        setupFinancialFilters();

        // عرض البيانات المالية
        displayStudentPayments();
        displayTeacherSalariesFinancial();
        displayStaffSalariesFinancial();
        displayTransportFees();

        // إعداد النوافذ المنبثقة
        initFinancialModals();
    }

    function setupFinancialFilters() {
        const studentSearch = document.getElementById('student-search');
        const monthFilter = document.getElementById('month-filter');
        const statusFilter = document.getElementById('payment-status-filter');

        if (studentSearch) {
            studentSearch.addEventListener('input', () => {
                displayStudentPayments();
            });
        }

        if (monthFilter) {
            monthFilter.addEventListener('change', () => {
                displayStudentPayments();
            });
        }

        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                displayStudentPayments();
            });
        }
    }

    function displayStudentPayments() {
        const studentPaymentsList = document.getElementById('student-payments-list');
        if (!studentPaymentsList) return;

        const searchTerm = document.getElementById('student-search')?.value.toLowerCase() || '';
        const monthFilter = document.getElementById('month-filter')?.value || '';
        const statusFilter = document.getElementById('payment-status-filter')?.value || '';

        // حساب الإحصائيات
        let totalFees = 0;
        let totalPaid = 0;
        let totalRemaining = 0;

        // فلترة التلاميذ
        const filteredStudents = students.filter(student => {
            return student.name.toLowerCase().includes(searchTerm) ||
                   student.barcode.includes(searchTerm);
        });

        let html = `
            <div class="financial-table">
                <table>
                    <thead>
                        <tr>
                            <th>الرقم التسلسلي</th>
                            <th>اسم التلميذ</th>
                            <th>المستوى</th>
                            <th>الفوج</th>
                            <th>المستحقات الشهرية</th>
                            <th>النقل</th>
                            <th>إجمالي المدفوع</th>
                            <th>إجمالي الباقي</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        filteredStudents.forEach(student => {
            let studentPaid = 0;
            let studentRemaining = 0;
            let studentTotal = 0;

            if (student.monthlyPayments) {
                Object.entries(student.monthlyPayments).forEach(([month, payment]) => {
                    if (!monthFilter || month === monthFilter) {
                        if (!statusFilter || payment.status === statusFilter) {
                            studentPaid += payment.amount || 0;
                            studentRemaining += payment.remaining || 0;
                            studentTotal += payment.dueAmount || 0;
                        }
                    }
                });
            }

            totalFees += studentTotal;
            totalPaid += studentPaid;
            totalRemaining += studentRemaining;

            html += `
                <tr>
                    <td>${student.barcode.padStart(6, '0')}</td>
                    <td>${student.name}</td>
                    <td>${student.level}</td>
                    <td>${student.group}</td>
                    <td>${student.fee} DHS</td>
                    <td>${student.hasTransport ? `${student.transportFee} DHS` : 'لا'}</td>
                    <td class="paid">${studentPaid.toLocaleString()} DHS</td>
                    <td class="${studentRemaining > 0 ? 'unpaid' : 'paid'}">${studentRemaining.toLocaleString()} DHS</td>
                    <td>
                        <button class="quick-pay-btn" onclick="openQuickPayment('${student.barcode}')">دفع سريع</button>
                        <button class="view-details-btn" onclick="viewPaymentDetails('${student.barcode}')">التفاصيل</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        studentPaymentsList.innerHTML = html;

        // تحديث الإحصائيات
        updateFinancialSummary(totalFees, totalPaid, totalRemaining);
    }

    function updateFinancialSummary(totalFees, totalPaid, totalRemaining) {
        const totalFeesEl = document.getElementById('total-student-fees');
        const totalPaidEl = document.getElementById('total-paid-fees');
        const totalRemainingEl = document.getElementById('total-remaining-fees');
        const collectionPercentageEl = document.getElementById('collection-percentage');

        if (totalFeesEl) totalFeesEl.textContent = totalFees.toLocaleString() + ' DHS';
        if (totalPaidEl) totalPaidEl.textContent = totalPaid.toLocaleString() + ' DHS';
        if (totalRemainingEl) totalRemainingEl.textContent = totalRemaining.toLocaleString() + ' DHS';

        const percentage = totalFees > 0 ? ((totalPaid / totalFees) * 100).toFixed(1) : 0;
        if (collectionPercentageEl) collectionPercentageEl.textContent = percentage + '%';
    }

    function displayTeacherSalariesFinancial() {
        const teacherSalariesList = document.getElementById('teacher-salaries-list');
        if (!teacherSalariesList) return;

        let html = `
            <div class="financial-table">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الأستاذ</th>
                            <th>الراتب الشهري</th>
                            <th>إجمالي المدفوع</th>
                            <th>إجمالي الباقي</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        teachers.forEach(teacher => {
            let teacherPaid = 0;
            let teacherRemaining = 0;

            if (teacher.monthlySalaries) {
                Object.values(teacher.monthlySalaries).forEach(salary => {
                    teacherPaid += salary.amount || 0;
                    teacherRemaining += salary.remaining || 0;
                });
            } else {
                teacherRemaining = parseFloat(teacher.salary || 0) * 10;
            }

            html += `
                <tr>
                    <td>${teacher.name}</td>
                    <td>${parseFloat(teacher.salary || 0).toLocaleString()} DHS</td>
                    <td class="paid">${teacherPaid.toLocaleString()} DHS</td>
                    <td class="${teacherRemaining > 0 ? 'unpaid' : 'paid'}">${teacherRemaining.toLocaleString()} DHS</td>
                    <td>
                        <button class="salary-pay-btn" onclick="payTeacherSalary('${teacher.name}')">دفع راتب</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        teacherSalariesList.innerHTML = html;
    }

    function displayStaffSalariesFinancial() {
        // سيتم تطوير هذه الوظيفة
    }

    function displayTransportFees() {
        const transportFeesList = document.getElementById('transport-fees-list');
        if (!transportFeesList) return;

        const transportStudents = students.filter(student => student.hasTransport);

        let html = `
            <div class="financial-table">
                <table>
                    <thead>
                        <tr>
                            <th>الرقم التسلسلي</th>
                            <th>اسم التلميذ</th>
                            <th>رسوم النقل الشهرية</th>
                            <th>إجمالي المدفوع</th>
                            <th>إجمالي الباقي</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        transportStudents.forEach(student => {
            // حساب رسوم النقل المدفوعة والمتبقية
            let transportPaid = 0;
            let transportRemaining = 0;
            const monthlyTransportFee = parseFloat(student.transportFee || 0);

            // هنا يمكن إضافة منطق حساب رسوم النقل المدفوعة
            transportRemaining = monthlyTransportFee * 10; // افتراضياً غير مدفوع

            html += `
                <tr>
                    <td>${student.barcode.padStart(6, '0')}</td>
                    <td>${student.name}</td>
                    <td>${monthlyTransportFee.toLocaleString()} DHS</td>
                    <td class="paid">${transportPaid.toLocaleString()} DHS</td>
                    <td class="unpaid">${transportRemaining.toLocaleString()} DHS</td>
                    <td>
                        <button class="transport-pay-btn" onclick="payTransportFee('${student.barcode}')">دفع رسوم النقل</button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        transportFeesList.innerHTML = html;
    }

    function initFinancialModals() {
        // إعداد النوافذ المنبثقة للدفع السريع
        document.querySelectorAll('.close-modal, .cancel-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.modal').forEach(modal => {
                    modal.style.display = 'none';
                });
            });
        });
    }

    // دوال الدفع السريع
    window.openQuickPayment = function(barcode) {
        const student = students.find(s => s.barcode === barcode);
        if (!student) return;

        const modal = document.getElementById('quick-payment-modal');
        const paymentInfo = document.getElementById('quick-payment-info');
        const monthSelect = document.getElementById('payment-month');

        paymentInfo.innerHTML = `
            <h4>${student.name} - ${student.level} - ${student.group}</h4>
            <p>المستحقات الشهرية: ${student.fee} DHS</p>
        `;

        // ملء قائمة الأشهر
        monthSelect.innerHTML = '<option value="">اختر الشهر</option>';
        ACADEMIC_MONTHS.forEach(month => {
            const option = document.createElement('option');
            option.value = month;
            option.textContent = month;
            monthSelect.appendChild(option);
        });

        modal.style.display = 'block';
    };

    window.viewPaymentDetails = function(barcode) {
        // فتح نافذة تفاصيل الدفعات (نفس النافذة الموجودة في صفحة التلاميذ)
        const student = students.find(s => s.barcode === barcode);
        if (student) {
            openPaymentsModal(student);
        }
    };

    window.payTeacherSalary = function(teacherName) {
        alert(`سيتم تفعيل دفع راتب ${teacherName} قريباً`);
    };

    window.payTransportFee = function(barcode) {
        alert('سيتم تفعيل دفع رسوم النقل قريباً');
    };
});