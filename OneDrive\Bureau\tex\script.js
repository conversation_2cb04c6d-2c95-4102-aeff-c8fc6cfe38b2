document.addEventListener('DOMContentLoaded', () => {
    // =================================================================
    //                      Constants & Storage
    // =================================================================
    const ACADEMIC_LEVELS = [
        "التمهيدي الأول", "التمهيدي الثاني", "التمهيدي الثالث",
        "الأول ابتدائي", "الثاني ابتدائي", "الثالث ابتدائي", "الرابع ابتدائي", "الخامس ابتدائي", "السادس ابتدائي",
        "الأولى إعدادي", "الثانية إعدادي", "الثالثة إعدادي"
    ];

    function getFromStorage(key) {
        return JSON.parse(localStorage.getItem(key)) || [];
    }

    function saveToStorage(key, data) {
        localStorage.setItem(key, JSON.stringify(data));
    }

    let students = getFromStorage('sbea_students');
    let groups = getFromStorage('sbea_groups');
    let teachers = getFromStorage('sbea_teachers');
    let activities = getFromStorage('sbea_activities');

    // قائمة شاملة لجميع المواد الدراسية
    const ALL_SUBJECTS = [
        "اللغة العربية",
        "اللغة الفرنسية",
        "اللغة الإنجليزية",
        "الرياضيات",
        "الفيزياء والكيمياء",
        "علوم الحياة والأرض",
        "النشاط العلمي",
        "الاجتماعيات",
        "التربية الإسلامية",
        "التربية الفنية",
        "التربية البدنية"
    ];

    const SUBJECTS_BY_LEVEL = {
        "الأول ابتدائي": {
            "اللغة العربية": ["التعبير الكتابي", "تمارين كتابية", "القراءة", "الإملاء", "الخط", "الاستماع والتحدث"],
            "اللغة الفرنسية": ["Dictée", "Activités orales", "Poésie", "Lecture", "Écriture/Copie", "Exercices écrits", "Projet de classe"],
            "مواد أخرى": ["التربية الإسلامية", "النشاط العلمي", "التربية الفنية", "الرياضيات", "التربية البدنية"]
        },
        "الابتدائي (2-6)": {
            "اللغة العربية": ["التعبير الكتابي", "تمارين كتابية", "القراءة", "الإملاء", "الخط", "الاستماع والتحدث"],
            "اللغة الفرنسية": ["Dictée", "Activités orales", "Poésie", "Lecture", "Écriture/Copie", "Exercices écrits", "Projet de classe"],
            "مواد أخرى": ["التربية الإسلامية", "النشاط العلمي", "التربية الفنية", "الرياضيات", "التربية البدنية", "اللغة الإنجليزية"]
        },
        "الإعدادي": {
            "مواد": ["الاجتماعيات", "التربية الإسلامية", "التربية البدنية", "الرياضيات", "الفيزياء والكيمياء", "اللغة الإنجليزية", "اللغة العربية", "اللغة الفرنسية", "علوم الحياة والأرض"]
        }
    };

    // =================================================================
    //                      Page Routing
    // =================================================================
    const currentPage = window.location.pathname.split('/').pop();

    if (currentPage === 'index.html' || currentPage === '') {
        initDashboardPage();
    } else if (currentPage === 'students.html') {
        initStudentsPage();
    } else if (currentPage === 'teachers.html') {
        initTeachersPage();
    } else if (currentPage === 'groups.html') {
        initGroupsPage();
    } else if (currentPage === 'activities.html') {
        initActivitiesPage();
    } else if (currentPage === 'settings.html') {
        initSettingsPage();
    }

    // =================================================================
    //                      Dashboard Page
    // =================================================================
    function initDashboardPage() {
        document.getElementById('total-students').textContent = students.length;
        document.getElementById('total-groups').textContent = groups.length;
        document.getElementById('total-teachers').textContent = teachers.length;
        document.getElementById('total-fees').textContent = students.reduce((sum, s) => sum + parseFloat(s.fee || 0), 0).toFixed(2);
        document.getElementById('paid-fees').textContent = students.filter(s => s.paid).reduce((sum, s) => sum + parseFloat(s.fee || 0), 0).toFixed(2);

        // Initialize search functionality
        initSearchFunctionality();

        // Add test data if storage is empty (for testing purposes)
        if (students.length === 0) {
            students = [
                {
                    name: "أحمد محمد علي",
                    level: "الثالث ابتدائي",
                    group: "فوج أ",
                    fee: "5000",
                    paid: true,
                    phone: "0555123456",
                    barcode: "000001",
                    picture: null
                },
                {
                    name: "فاطمة الزهراء",
                    level: "الخامس ابتدائي",
                    group: "فوج ب",
                    fee: "6000",
                    paid: false,
                    phone: "0666789012",
                    barcode: "000002",
                    picture: null
                },
                {
                    name: "يوسف عبد الرحمن",
                    level: "الأولى إعدادي",
                    group: "فوج أ",
                    fee: "7000",
                    paid: true,
                    phone: "0777345678",
                    barcode: "000003",
                    picture: null
                }
            ];
            saveToStorage('sbea_students', students);
        }

        if (teachers.length === 0) {
            teachers = [
                {
                    name: "الأستاذ محمد الأمين",
                    salary: "50000",
                    tasks: "تدريس الرياضيات والفيزياء",
                    levels: ["الثالث ابتدائي", "الخامس ابتدائي"],
                    groups: ["فوج أ", "فوج ب"],
                    subjects: ["الرياضيات", "الفيزياء والكيمياء"]
                },
                {
                    name: "الأستاذة خديجة بن علي",
                    salary: "45000",
                    tasks: "تدريس اللغة العربية",
                    levels: ["الأولى إعدادي", "الثانية إعدادي"],
                    groups: ["فوج أ"],
                    subjects: ["اللغة العربية", "التربية الإسلامية"]
                }
            ];
            saveToStorage('sbea_teachers', teachers);
        }

        if (groups.length === 0) {
            groups = [
                { level: "الثالث ابتدائي", name: "فوج أ" },
                { level: "الثالث ابتدائي", name: "فوج ب" },
                { level: "الخامس ابتدائي", name: "فوج أ" },
                { level: "الخامس ابتدائي", name: "فوج ب" },
                { level: "الأولى إعدادي", name: "فوج أ" },
                { level: "الأولى إعدادي", name: "فوج ب" },
                { level: "الثانية إعدادي", name: "فوج أ" },
                { level: "الثانية إعدادي", name: "فوج ب" }
            ];
            saveToStorage('sbea_groups', groups);
        }

        // Update dashboard stats with new data
        document.getElementById('total-students').textContent = students.length;
        document.getElementById('total-groups').textContent = groups.length;
        document.getElementById('total-teachers').textContent = teachers.length;
        document.getElementById('total-fees').textContent = students.reduce((sum, s) => sum + parseFloat(s.fee || 0), 0).toFixed(2);
        document.getElementById('paid-fees').textContent = students.filter(s => s.paid).reduce((sum, s) => sum + parseFloat(s.fee || 0), 0).toFixed(2);
    }

    function initSearchFunctionality() {
        const searchNameInput = document.getElementById('search-name');
        const searchLevelInput = document.getElementById('search-level');
        const searchBarcodeInput = document.getElementById('search-barcode');
        const searchTypeInput = document.getElementById('search-type');
        const searchBtn = document.getElementById('search-btn');
        const clearSearchBtn = document.getElementById('clear-search-btn');
        const searchResults = document.getElementById('search-results');

        // Populate levels dropdown
        ACADEMIC_LEVELS.forEach(level => {
            const option = document.createElement('option');
            option.value = level;
            option.textContent = level;
            searchLevelInput.appendChild(option);
        });

        function performSearch() {
            const nameQuery = searchNameInput.value.toLowerCase().trim();
            const levelQuery = searchLevelInput.value;
            const barcodeQuery = searchBarcodeInput.value.trim();
            const typeQuery = searchTypeInput.value;

            let results = [];

            // Search students
            if (typeQuery === 'all' || typeQuery === 'students') {
                const studentResults = students.filter(student => {
                    const nameMatch = !nameQuery || student.name.toLowerCase().includes(nameQuery);
                    const levelMatch = !levelQuery || student.level === levelQuery;
                    const barcodeMatch = !barcodeQuery || student.barcode.includes(barcodeQuery);
                    return nameMatch && levelMatch && barcodeMatch;
                }).map(student => ({
                    type: 'student',
                    name: student.name,
                    level: student.level,
                    group: student.group,
                    barcode: student.barcode,
                    phone: student.phone,
                    fee: student.fee,
                    paid: student.paid,
                    picture: student.picture
                }));
                results = results.concat(studentResults);
            }

            // Search teachers
            if (typeQuery === 'all' || typeQuery === 'teachers') {
                const teacherResults = teachers.filter(teacher => {
                    const nameMatch = !nameQuery || teacher.name.toLowerCase().includes(nameQuery);
                    const levelMatch = !levelQuery || teacher.levels.includes(levelQuery);
                    return nameMatch && levelMatch;
                }).map(teacher => ({
                    type: 'teacher',
                    name: teacher.name,
                    levels: teacher.levels,
                    groups: teacher.groups,
                    subjects: teacher.subjects,
                    salary: teacher.salary,
                    tasks: teacher.tasks
                }));
                results = results.concat(teacherResults);
            }

            displayResults(results);
        }

        function displayResults(results) {
            if (results.length === 0) {
                searchResults.innerHTML = '<p class="no-results">لم يتم العثور على نتائج مطابقة للبحث.</p>';
                return;
            }

            let html = '<h3>نتائج البحث:</h3><div class="results-grid">';

            results.forEach(item => {
                if (item.type === 'student') {
                    html += `
                        <div class="result-card student-card">
                            <div class="result-header">
                                <img src="${item.picture || 'logo.jpg'}" alt="صورة ${item.name}" class="result-image">
                                <h4>${item.name}</h4>
                                <span class="result-type">تلميذ</span>
                            </div>
                            <div class="result-details">
                                <p><strong>المستوى:</strong> ${item.level}</p>
                                <p><strong>الفوج:</strong> ${item.group}</p>
                                <p><strong>الرمز الشريطي:</strong> ${item.barcode}</p>
                                <p><strong>الهاتف:</strong> ${item.phone}</p>
                                <p><strong>المستحقات:</strong> ${item.fee} دج</p>
                                <p class="${item.paid ? 'paid' : 'unpaid'}"><strong>الحالة:</strong> ${item.paid ? 'مدفوع' : 'غير مدفوع'}</p>
                            </div>
                        </div>
                    `;
                } else if (item.type === 'teacher') {
                    html += `
                        <div class="result-card teacher-card">
                            <div class="result-header">
                                <img src="logo.jpg" alt="صورة ${item.name}" class="result-image">
                                <h4>${item.name}</h4>
                                <span class="result-type">أستاذ</span>
                            </div>
                            <div class="result-details">
                                <p><strong>المستويات:</strong> ${item.levels.join(', ')}</p>
                                <p><strong>الأفواج:</strong> ${item.groups.join(', ')}</p>
                                <p><strong>المواد:</strong> ${item.subjects.join(', ')}</p>
                                <p><strong>الراتب:</strong> ${item.salary || 'غير محدد'}</p>
                                <p><strong>المهام:</strong> ${item.tasks || 'غير محددة'}</p>
                            </div>
                        </div>
                    `;
                }
            });

            html += '</div>';
            searchResults.innerHTML = html;
        }

        function clearSearch() {
            searchNameInput.value = '';
            searchLevelInput.value = '';
            searchBarcodeInput.value = '';
            searchTypeInput.value = 'all';
            searchResults.innerHTML = '';
        }

        // Event listeners
        searchBtn.addEventListener('click', performSearch);
        clearSearchBtn.addEventListener('click', clearSearch);

        // Real-time search on input
        searchNameInput.addEventListener('input', performSearch);
        searchLevelInput.addEventListener('change', performSearch);
        searchBarcodeInput.addEventListener('input', performSearch);
        searchTypeInput.addEventListener('change', performSearch);
    }

    // =================================================================
    //                      Students Page
    // =================================================================
    function initStudentsPage() {
        const studentForm = document.getElementById('add-student-form');
        const studentTableBody = document.querySelector('#student-table tbody');
        const searchNameInput = document.getElementById('search-name');
        const searchLevelInput = document.getElementById('search-level');
        const searchBarcodeInput = document.getElementById('search-barcode');
        const printStudentsBtn = document.getElementById('print-students-btn');
        const studentLevelSelect = document.getElementById('student-level');
        const studentGroupSelect = document.getElementById('student-group');
        const importExcelBtn = document.getElementById('import-excel-btn');
        const excelFileInput = document.getElementById('excel-file-input');
        const downloadTemplateBtn = document.getElementById('download-template-btn');

        function populateLevelsDropdown(selectElement) {
            ACADEMIC_LEVELS.forEach(level => {
                const option = document.createElement('option');
                option.value = level;
                option.textContent = level;
                selectElement.appendChild(option.cloneNode(true));
            });
             // Also populate search dropdown
            ACADEMIC_LEVELS.forEach(level => {
                const option = document.createElement('option');
                option.value = level;
                option.textContent = level;
                searchLevelInput.appendChild(option);
            });
        }

        function updateGroupsDropdown(selectedLevel) {
            studentGroupSelect.innerHTML = '<option value="">اختر الفوج</option>';
            const filteredGroups = groups.filter(g => g.level === selectedLevel);
            filteredGroups.forEach(group => {
                const option = document.createElement('option');
                option.value = group.name;
                option.textContent = group.name;
                studentGroupSelect.appendChild(option);
            });
        }

        studentLevelSelect.addEventListener('change', () => {
            updateGroupsDropdown(studentLevelSelect.value);
        });

        function renderStudents() {
            const nameFilter = searchNameInput.value.toLowerCase();
            const levelFilter = searchLevelInput.value;
            const barcodeFilter = searchBarcodeInput.value;

            const filteredStudents = students.filter(s => {
                const nameMatch = s.name.toLowerCase().includes(nameFilter);
                const levelMatch = !levelFilter || s.level === levelFilter;
                const barcodeMatch = !barcodeFilter || s.barcode.includes(barcodeFilter);
                return nameMatch && levelMatch && barcodeMatch;
            });

            studentTableBody.innerHTML = '';
            filteredStudents.forEach((student, index) => {
                addStudentToTable(student, index + 1);
            });
        }

        function addStudentToTable(student, serialNumber) {
            const { name, level, group, fee, paid, phone, barcode, picture } = student;
            const paddedSerialNumber = serialNumber.toString().padStart(6, '0');
            const row = document.createElement('tr');
            // Use barcode as the unique identifier
            row.dataset.barcode = barcode;

            row.innerHTML = `
                <td><img src="${picture || 'logo.jpg'}" alt="صورة شخصية" class="student-picture-thumbnail"></td>
                <td>${paddedSerialNumber}</td>
                <td>${name}</td>
                <td>${level}</td>
                <td>${group}</td>
                <td>${fee}</td>
                <td class="${paid ? 'paid' : 'unpaid'}">${paid ? 'مدفوع' : 'غير مدفوع'}</td>
                <td>${phone}</td>
                <td><svg id="barcode-${barcode}"></svg></td>
                <td>
                    <button class="edit-btn">تعديل</button>
                    <button class="delete-btn">حذف</button>
                    <button class="toggle-status-btn">تغيير الحالة</button>
                    <button class="whatsapp-btn">WhatsApp</button>
                </td>
            `;
            studentTableBody.appendChild(row);
            // Use the student's own barcode property for the JsBarcode
            if (barcode) JsBarcode(`#barcode-${barcode}`, barcode, { height: 40, displayValue: true, margin: 0, fontSize: 14 });

            row.querySelector('.delete-btn').addEventListener('click', (e) => {
                const targetBarcode = e.target.closest('tr').dataset.barcode;
                const studentIndex = students.findIndex(s => s.barcode === targetBarcode);
                if (studentIndex > -1) {
                    students.splice(studentIndex, 1);
                    saveToStorage('sbea_students', students);
                    renderStudents(searchInput.value.toLowerCase());
                }
            });
            row.querySelector('.toggle-status-btn').addEventListener('click', (e) => {
                const targetBarcode = e.target.closest('tr').dataset.barcode;
                const studentIndex = students.findIndex(s => s.barcode === targetBarcode);
                if (studentIndex > -1) {
                    students[studentIndex].paid = !students[studentIndex].paid;
                    saveToStorage('sbea_students', students);
                    renderStudents(searchInput.value.toLowerCase());
                }
            });
            row.querySelector('.whatsapp-btn').addEventListener('click', () => {
                const internationalPhone = phone.startsWith('0') ? `213${phone.substring(1)}` : phone;
                window.open(`https://wa.me/${internationalPhone}`, '_blank');
            });
            row.querySelector('.edit-btn').addEventListener('click', e => {
                alert("سيتم تفعيل ميزة التعديل قريباً.");
            });
        }

        studentForm.addEventListener('submit', async e => {
            e.preventDefault();
            const pictureInput = document.getElementById('student-picture');
            let pictureDataUrl = null;

            if (pictureInput.files && pictureInput.files[0]) {
                try {
                    pictureDataUrl = await new Promise((resolve, reject) => {
                        const reader = new FileReader();
                        reader.onload = () => resolve(reader.result);
                        reader.onerror = reject;
                        reader.readAsDataURL(pictureInput.files[0]);
                    });
                } catch (error) {
                    console.error("Error reading file:", error);
                    alert("حدث خطأ أثناء تحميل الصورة.");
                    return;
                }
            }

            const newStudent = {
                name: document.getElementById('student-name').value.trim(),
                level: document.getElementById('student-level').value,
                group: document.getElementById('student-group').value,
                fee: document.getElementById('student-fee').value.trim(),
                paid: false,
                phone: document.getElementById('student-phone').value.trim(),
                picture: pictureDataUrl,
                barcode: '' // Will be set after pushing to the array
            };
            
            students.push(newStudent);
            // Assign barcode based on the final index + 1 (the serial number)
            const newIndex = students.length;
            newStudent.barcode = newIndex.toString().padStart(6, '0');

            saveToStorage('sbea_students', students);
            renderStudents();
            studentForm.reset();
            studentGroupSelect.innerHTML = '<option value="">اختر الفوج</option>';
        });

        importExcelBtn.addEventListener('click', () => excelFileInput.click());
        excelFileInput.addEventListener('change', e => {
            const file = e.target.files[0];
            const reader = new FileReader();
            reader.onload = (event) => {
                const data = new Uint8Array(event.target.result);
                const workbook = XLSX.read(data, { type: 'array' });
                const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
                const jsonData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });
                
                jsonData.slice(1).forEach(row => {
                    if (row[0] && row[1] && row[2] && row[3] && row[4]) {
                        const newIndex = students.length + 1;
                        students.push({
                            name: row[0], level: row[1], group: row[2], fee: row[3], paid: false, phone: row[4],
                            picture: null, // No picture from Excel import
                            barcode: newIndex.toString().padStart(6, '0')
                        });
                        const groupExists = groups.some(g => g.level === row[1] && g.name === row[2]);
                        if (!groupExists) {
                            groups.push({ level: row[1], name: row[2] });
                            saveToStorage('sbea_groups', groups);
                        }
                    }
                });
                saveToStorage('sbea_students', students);
                renderStudents();
                alert(`${jsonData.length - 1} تلميذ تم استيرادهم بنجاح!`);
            };
            reader.readAsArrayBuffer(file);
            excelFileInput.value = '';
        });

        downloadTemplateBtn.addEventListener('click', () => {
            const headers = [
                ["الاسم الكامل", "المستوى", "الفوج", "المستحقات الشهرية", "هاتف ولي الأمر"]
            ];
            const ws = XLSX.utils.aoa_to_sheet(headers);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "نموذج");
            XLSX.writeFile(wb, "نموذج_تلاميذ.xlsx");
        });

        searchNameInput.addEventListener('input', renderStudents);
        searchLevelInput.addEventListener('change', renderStudents);
        searchBarcodeInput.addEventListener('input', renderStudents);

        printStudentsBtn.addEventListener('click', () => window.print());

        populateLevelsDropdown(studentLevelSelect);
        renderStudents();
    }

    // =================================================================
    //                      Teachers Page
    // =================================================================
    function initTeachersPage() {
        const teacherForm = document.getElementById('add-teacher-form');
        const teacherTableBody = document.querySelector('#teacher-table tbody');
        const teacherNameInput = document.getElementById('teacher-name');
        const teacherSalaryInput = document.getElementById('teacher-salary');
        const teacherTasksInput = document.getElementById('teacher-tasks');
        const levelsChecklist = document.getElementById('teacher-levels-checklist');
        const groupsChecklist = document.getElementById('teacher-groups-checklist');
        const subjectsChecklist = document.getElementById('teacher-subjects-checklist');

        function populateChecklist(container, items, name) {
            container.innerHTML = `<h4>${name}</h4>`;
            if (items.length === 0) {
                container.innerHTML += '<p>لا توجد عناصر متاحة. يرجى إضافتها أولاً.</p>';
                return;
            }
            items.forEach(item => {
                const checkboxId = `${name}-${item.replace(/\s+/g, '-')}`;
                const div = document.createElement('div');
                div.innerHTML = `
                    <input type="checkbox" id="${checkboxId}" name="${name}" value="${item}">
                    <label for="${checkboxId}">${item}</label>
                `;
                container.appendChild(div);
            });
        }

        function getUniqueSubjects() {
            // استخدام القائمة الشاملة للمواد
            return ALL_SUBJECTS;
        }

        function renderTeachers() {
            teacherTableBody.innerHTML = '';
            teachers.forEach((teacher, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${teacher.name}</td>
                    <td>${teacher.salary || 'N/A'}</td>
                    <td>${teacher.levels.join(', ')}</td>
                    <td>${teacher.groups.join(', ')}</td>
                    <td>${teacher.subjects.join(', ')}</td>
                    <td>${teacher.tasks || 'N/A'}</td>
                    <td><button class="delete-btn">حذف</button></td>
                `;
                row.querySelector('.delete-btn').addEventListener('click', () => {
                    teachers.splice(index, 1);
                    saveToStorage('sbea_teachers', teachers);
                    renderTeachers();
                });
                teacherTableBody.appendChild(row);
            });
        }

        teacherForm.addEventListener('submit', e => {
            e.preventDefault();
            
            const getSelected = (container) => {
                return Array.from(container.querySelectorAll('input[type="checkbox"]:checked')).map(cb => cb.value);
            };

            const newTeacher = {
                name: teacherNameInput.value.trim(),
                salary: teacherSalaryInput.value.trim(),
                tasks: teacherTasksInput.value.trim(),
                levels: getSelected(levelsChecklist),
                groups: getSelected(groupsChecklist),
                subjects: getSelected(subjectsChecklist)
            };

            teachers.push(newTeacher);
            saveToStorage('sbea_teachers', teachers);
            renderTeachers();
            teacherForm.reset();
            // Also uncheck all checkboxes
            Array.from(teacherForm.querySelectorAll('input[type="checkbox"]')).forEach(cb => cb.checked = false);
        });

        populateChecklist(levelsChecklist, ACADEMIC_LEVELS, 'المستويات');
        populateChecklist(groupsChecklist, groups.map(g => g.name), 'الأفواج');
        populateChecklist(subjectsChecklist, getUniqueSubjects(), 'المواد');
        renderTeachers();
    }

    // =================================================================
    //                      Groups Page (Levels & Groups)
    // =================================================================
    function initGroupsPage() {
        const groupForm = document.getElementById('add-group-form');
        const levelSelect = document.getElementById('level-select');
        const groupNameInput = document.getElementById('group-name');
        const listContainer = document.getElementById('levels-and-groups-list');

        function populateLevelsDropdown() {
            ACADEMIC_LEVELS.forEach(level => {
                levelSelect.appendChild(new Option(level, level));
            });
        }

        function getSubjectsForLevel(level) {
            if (level === "الأول ابتدائي") return SUBJECTS_BY_LEVEL["الأول ابتدائي"];
            if (level.includes("ابتدائي") && level !== "الأول ابتدائي") return SUBJECTS_BY_LEVEL["الابتدائي (2-6)"];
            if (level.includes("إعدادي")) return SUBJECTS_BY_LEVEL["الإعدادي"];
            return null;
        }

        function renderLevelsAndGroups() {
            listContainer.innerHTML = '';
            ACADEMIC_LEVELS.forEach(level => {
                const levelContainer = document.createElement('div');
                levelContainer.className = 'level-container';
                
                let subjectsHTML = '<h4>المواد الدراسية:</h4>';
                const subjects = getSubjectsForLevel(level);
                if (subjects) {
                    subjectsHTML += '<div class="subjects-grid">';
                    for (const category in subjects) {
                        subjectsHTML += `<div class="subject-category"><h5>${category}</h5><ul>`;
                        subjects[category].forEach(subject => {
                            subjectsHTML += `<li>${subject}</li>`;
                        });
                        subjectsHTML += '</ul></div>';
                    }
                    subjectsHTML += '</div>';
                } else {
                    subjectsHTML += '<p>لا توجد مواد محددة لهذا المستوى.</p>';
                }

                levelContainer.innerHTML = `<h3>${level}</h3>${subjectsHTML}`;

                const ul = document.createElement('ul');
                ul.innerHTML = '<h4>الأفواج:</h4>';
                const levelGroups = groups.filter(g => g.level === level);
                if (levelGroups.length > 0) {
                    levelGroups.forEach(group => {
                        const li = document.createElement('li');
                        li.textContent = `الفوج: ${group.name}`;
                        const deleteBtn = document.createElement('button');
                        deleteBtn.textContent = 'حذف';
                        deleteBtn.className = 'delete-btn';
                        deleteBtn.onclick = () => {
                            const groupIndex = groups.findIndex(g => g.level === level && g.name === group.name);
                            if (groupIndex > -1) {
                                groups.splice(groupIndex, 1);
                                saveToStorage('sbea_groups', groups);
                                renderLevelsAndGroups();
                            }
                        };
                        li.appendChild(deleteBtn);
                        ul.appendChild(li);
                    });
                } else {
                    const li = document.createElement('li');
                    li.textContent = 'لا توجد أفواج في هذا المستوى.';
                    ul.appendChild(li);
                }
                levelContainer.appendChild(ul);
                listContainer.appendChild(levelContainer);
            });
        }

        groupForm.addEventListener('submit', e => {
            e.preventDefault();
            const level = levelSelect.value;
            const name = groupNameInput.value.trim();
            if (level && name && !groups.some(g => g.level === level && g.name === name)) {
                groups.push({ level, name });
                saveToStorage('sbea_groups', groups);
                renderLevelsAndGroups();
                groupNameInput.value = '';
            } else {
                alert('هذا الفوج موجود بالفعل في هذا المستوى.');
            }
        });

        populateLevelsDropdown();
        renderLevelsAndGroups();
    }

    // =================================================================
    //                      Activities Page
    // =================================================================
    function initActivitiesPage() {
        const activityForm = document.getElementById('add-activity-form');
        const activitiesList = document.getElementById('activities-list');

        function renderActivities() {
            activitiesList.innerHTML = '';
            activities.forEach((activity, index) => {
                const card = document.createElement('div');
                card.className = 'activity-card';
                const mediaHTML = activity.media.map(src => {
                    if (src.startsWith('data:image')) {
                        return `<img src="${src}" alt="Activity media">`;
                    } else if (src.startsWith('data:video')) {
                        return `<video controls src="${src}"></video>`;
                    }
                    return '';
                }).join('');

                card.innerHTML = `
                    <h3>${activity.name}</h3>
                    <p><strong>الفئة المستهدفة:</strong> ${activity.target}</p>
                    <p><strong>المكان:</strong> ${activity.location}</p>
                    <p><strong>التوقيت:</strong> ${new Date(activity.datetime).toLocaleString('ar-DZ')}</p>
                    <div class="media-container">${mediaHTML}</div>
                    <button class="delete-btn">حذف النشاط</button>
                `;
                card.querySelector('.delete-btn').addEventListener('click', () => {
                    activities.splice(index, 1);
                    saveToStorage('sbea_activities', activities);
                    renderActivities();
                });
                activitiesList.appendChild(card);
            });
        }

        activityForm.addEventListener('submit', e => {
            e.preventDefault();
            const mediaInput = document.getElementById('activity-media');
            const mediaFiles = Array.from(mediaInput.files);
            const mediaPromises = mediaFiles.map(file => {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result);
                    reader.onerror = reject;
                    reader.readAsDataURL(file);
                });
            });

            Promise.all(mediaPromises).then(mediaDataUrls => {
                const newActivity = {
                    name: document.getElementById('activity-name').value,
                    target: document.getElementById('activity-target').value,
                    location: document.getElementById('activity-location').value,
                    datetime: document.getElementById('activity-datetime').value,
                    media: mediaDataUrls
                };
                activities.push(newActivity);
                saveToStorage('sbea_activities', activities);
                renderActivities();
                activityForm.reset();
            });
        });

        renderActivities();
    }

    // =================================================================
    //                      Settings Page
    // =================================================================
    function initSettingsPage() {
        const backupBtn = document.getElementById('backup-btn');
        const restoreInput = document.getElementById('restore-input');

        backupBtn.addEventListener('click', () => {
            const data = {
                students: getFromStorage('sbea_students'),
                groups: getFromStorage('sbea_groups'),
                teachers: getFromStorage('sbea_teachers'),
                activities: getFromStorage('sbea_activities')
            };
            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(data));
            const dlAnchorElem = document.createElement('a');
            dlAnchorElem.setAttribute("href", dataStr);
            dlAnchorElem.setAttribute("download", `sbea_backup_${new Date().toISOString().split('T')[0]}.json`);
            dlAnchorElem.click();
        });

        restoreInput.addEventListener('change', e => {
            const file = e.target.files[0];
            if (!file) return;
            const reader = new FileReader();
            reader.onload = (event) => {
                try {
                    const data = JSON.parse(event.target.result);
                    if (data.students && data.groups && data.teachers) {
                        saveToStorage('sbea_students', data.students);
                        saveToStorage('sbea_groups', data.groups);
                        saveToStorage('sbea_teachers', data.teachers);
                        saveToStorage('sbea_activities', data.activities || []);
                        alert('تم استعادة البيانات بنجاح! قم بتحديث الصفحة لرؤية التغييرات.');
                    } else {
                        alert('ملف النسخ الاحتياطي غير صالح.');
                    }
                } catch (error) {
                    alert('خطأ في قراءة الملف.');
                }
            };
            reader.readAsText(file);
            restoreInput.value = '';
        });
    }
});