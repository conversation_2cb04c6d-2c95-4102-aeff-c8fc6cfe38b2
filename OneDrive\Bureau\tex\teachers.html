<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأساتذة - مؤسسة النور التربوي</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <img src="logo.png" alt="شعار مؤسسة النور التربوي" class="logo">
        <h1>مؤسسة النور التربوي للتعليم الخصوصي</h1>
        <nav>
            <ul>
                <li><a href="index.html">لوحة التحكم</a></li>
                <li><a href="students.html">إدارة التلاميذ</a></li>
                <li><a href="teachers.html" class="active">إدارة الأساتذة</a></li>
                <li><a href="groups.html">إدارة المستويات والأفواج</a></li>
                <li><a href="activities.html">إدارة الأنشطة</a></li>
                <li><a href="settings.html">الإعدادات</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section id="teacher-management">
            <h2>إدارة الأساتذة</h2>
            <form id="add-teacher-form" class="detailed-form">
                <div class="form-row">
                    <input type="text" id="teacher-name" placeholder="اسم الأستاذ" required>
                    <input type="number" id="teacher-salary" placeholder="الراتب الشهري" required>
                </div>
                <div class="form-row">
                     <textarea id="teacher-tasks" placeholder="مهام إضافية"></textarea>
                </div>

                <div class="form-row checklists-container">
                    <div id="teacher-levels-checklist" class="checklist-group">
                        <h4>المستويات</h4>
                        <!-- Checkboxes for levels will be populated by JS -->
                    </div>
                    <div id="teacher-groups-checklist" class="checklist-group">
                        <h4>الأفواج</h4>
                        <!-- Checkboxes for groups will be populated by JS -->
                    </div>
                    <div id="teacher-subjects-checklist" class="checklist-group">
                        <h4>المواد</h4>
                        <!-- Checkboxes for subjects will be populated by JS -->
                    </div>
                </div>
                
                <button type="submit">إضافة أستاذ</button>
            </form>

            <table id="teacher-table">
                <thead>
                    <tr>
                        <th>الرقم الترتيبي</th>
                        <th>اسم الأستاذ</th>
                        <th>الراتب الشهري</th>
                        <th>المستويات</th>
                        <th>الأفواج</th>
                        <th>المواد</th>
                        <th>مهام إضافية</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Teacher rows will be inserted here -->
                </tbody>
            </table>
        </section>
    </main>

    <footer>
        <p>مؤسسة النور التربوي - الخيار الأمثل لتسيير مؤسسات التعليم الخاصة.</p>
    </footer>

    <script src="script.js"></script>
</body>
</html>