body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
    color: #333;
    direction: rtl;
}

header {
   background-color: #007bff;
   color: white;
   padding: 1rem;
   box-shadow: 0 2px 4px rgba(0,0,0,0.1);
   display: flex;
   align-items: center;
   justify-content: center;
   gap: 15px;
}

header .logo {
   height: 50px;
   width: 50px;
}

header h1 {
    margin: 0;
    font-size: 2rem;
}

nav ul {
    list-style: none;
    padding: 0;
    margin: 1rem 0 0;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
}

nav a {
    color: white;
    text-decoration: none;
    padding: 10px 15px;
    border-radius: 5px;
    transition: background-color 0.3s;
}

nav a:hover, nav a.active {
    background-color: #0056b3;
}

main {
    padding: 1rem;
    max-width: 1200px;
    margin: 2rem auto;
}

section {
    background-color: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

h2 {
    color: #0056b3;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
    margin-top: 0;
}

h3 {
    color: #0069d9;
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}

form {
    margin-bottom: 2rem;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}

input[type="text"], input[type="number"], input[type="tel"], input[type="datetime-local"], select {
    flex-grow: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-width: 150px;
}

.form-full-width {
    width: 100%;
    margin-top: 10px;
}

.toolbar {
    display: flex;
    gap: 10px;
    margin-bottom: 1rem;
}

#search-student {
    flex-grow: 1;
}

button {
    background-color: #28a745;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #218838;
}

.secondary-btn {
    background-color: #6c757d;
}

.secondary-btn:hover {
    background-color: #5a6268;
}

.edit-btn {
    background-color: #007bff;
    margin-left: 5px;
}

.edit-btn:hover {
    background-color: #0056b3;
}

.delete-btn {
    background-color: #dc3545;
    margin-right: auto;
}

.delete-btn:hover {
    background-color: #c82333;
}

.toggle-status-btn {
    background-color: #ffc107;
    color: #333;
}

.toggle-status-btn:hover {
    background-color: #e0a800;
}

.paid {
    color: green;
    font-weight: bold;
}

.unpaid {
    color: red;
    font-weight: bold;
}

.whatsapp-btn {
    background-color: #25D366;
}

.whatsapp-btn:hover {
    background-color: #128C7E;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

th, td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
}

td button {
    margin-right: 5px;
}

td .student-picture-thumbnail {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

footer {
    text-align: center;
    padding: 1rem;
    margin-top: 2rem;
    background-color: #e9ecef;
    color: #6c757d;
}

#levels-and-groups-list .level-container {
    margin-bottom: 1.5rem;
    padding: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
}

#levels-and-groups-list ul {
    list-style: none;
    padding: 0;
}

#levels-and-groups-list li {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#dashboard .stats-container, #activities-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

.stat-card, .activity-card {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
}

.activity-card h3 {
    margin-top: 0;
    font-size: 1.3rem;
    color: #0056b3;
}

.activity-card p {
    margin: 0.5rem 0;
}

.activity-card .media-container {
    margin-top: 1rem;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.activity-card .media-container img, .activity-card .media-container video {
    max-width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 5px;
}

#print-students-btn {
    margin-top: 1rem;
    background-color: #6c757d;
}

#print-students-btn:hover {
    background-color: #5a6268;
}

@media print {
    body * {
        visibility: hidden;
    }
    #student-list-container, #student-list-container * {
        visibility: visible;
    }
    #student-list-container {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
    #student-table .delete-btn, #student-table .edit-btn, #student-table .toggle-status-btn, #student-table .whatsapp-btn {
        display: none;
    }
    #student-table th:last-child, #student-table td:last-child {
        display: none;
    }
}

#data-management .restore-wrapper {
    margin-top: 1rem;
    border-top: 1px solid #ddd;
    padding-top: 1rem;
}

/* Styles for Detailed Teacher Form */
.detailed-form .form-row {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    width: 100%;
}

.detailed-form .form-row > * {
    flex: 1;
}

.detailed-form textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-height: 80px;
    resize: vertical;
}

.checklists-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
    padding: 1rem;
    border: 1px solid #eee;
    border-radius: 5px;
}

.checklist-group h4 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    color: #0056b3;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.checklist-group div {
    display: block;
    margin-bottom: 5px;
}

.checklist-group label {
    margin-right: 10px;
    cursor: pointer;
}

/* Styles for Subjects Display in Groups Page */
.subjects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.subject-category {
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 5px;
}

.subject-category h5 {
    margin-top: 0;
    color: #0056b3;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.subject-category ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.subject-category li {
    padding: 5px 0;
}

/* Search Section Styles */
#search-section {
    background: white;
    margin: 20px;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#search-section h2 {
    color: #007bff;
    margin-bottom: 20px;
    text-align: center;
}

.search-container {
    margin-bottom: 20px;
}

.search-fields {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.search-field {
    display: flex;
    flex-direction: column;
}

.search-field label {
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.search-field input,
.search-field select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

#search-btn,
#clear-search-btn {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 10px;
    font-size: 14px;
}

#clear-search-btn {
    background-color: #6c757d;
}

#search-btn:hover {
    background-color: #0056b3;
}

#clear-search-btn:hover {
    background-color: #545b62;
}

/* Search Results Styles */
#search-results {
    margin-top: 20px;
}

#search-results h3 {
    color: #007bff;
    margin-bottom: 15px;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

.result-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.result-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.student-card {
    border-right: 4px solid #28a745;
}

.teacher-card {
    border-right: 4px solid #007bff;
}

.result-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    gap: 10px;
}

.result-image {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #ddd;
}

.result-header h4 {
    margin: 0;
    flex-grow: 1;
    color: #333;
}

.result-type {
    background-color: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.student-card .result-type {
    background-color: #28a745;
}

.result-details p {
    margin: 5px 0;
    font-size: 14px;
}

.result-details strong {
    color: #007bff;
}

.paid {
    color: #28a745;
    font-weight: bold;
}

.unpaid {
    color: #dc3545;
    font-weight: bold;
}

.no-results {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

/* Modal Styles */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.modal-header {
    background-color: #007bff;
    color: white;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
}

.close-modal {
    font-size: 24px;
    cursor: pointer;
    font-weight: bold;
}

.close-modal:hover {
    opacity: 0.7;
}

.modal-body {
    padding: 20px;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

.cancel-btn {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
}

.cancel-btn:hover {
    background-color: #545b62;
}

/* Student Summary */
.student-summary {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.student-summary h4 {
    margin: 0 0 10px 0;
    color: #007bff;
}

.student-summary p {
    margin: 5px 0;
}

/* Payments Table */
.payments-table {
    overflow-x: auto;
}

.payments-table table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.payments-table th,
.payments-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
}

.payments-table th {
    background-color: #007bff;
    color: white;
}

.payments-table .status-مدفوع {
    background-color: #d4edda;
    color: #155724;
    font-weight: bold;
}

.payments-table .status-جزئي {
    background-color: #fff3cd;
    color: #856404;
    font-weight: bold;
}

.payments-table .status-غير-مدفوع {
    background-color: #f8d7da;
    color: #721c24;
    font-weight: bold;
}

.pay-btn,
.partial-pay-btn {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    margin: 2px;
    font-size: 12px;
}

.partial-pay-btn {
    background-color: #ffc107;
    color: #212529;
}

.pay-btn:hover {
    background-color: #218838;
}

.partial-pay-btn:hover {
    background-color: #e0a800;
}

/* Transport Status */
.transport-yes {
    color: #28a745;
    font-weight: bold;
}

.transport-no {
    color: #6c757d;
}

/* Enhanced Table Styles */
#student-table th {
    background-color: #007bff;
    color: white;
    font-weight: bold;
    text-align: center;
}

#student-table td {
    text-align: center;
    vertical-align: middle;
}

.student-picture-thumbnail {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #ddd;
}

.payments-btn {
    background-color: #17a2b8;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    margin: 2px;
    font-size: 12px;
}

.payments-btn:hover {
    background-color: #138496;
}

/* Enhanced Dashboard Stats */
.stat-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-card.primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.stat-card.secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
}

.stat-card.success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.stat-card.warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

.stat-card.info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.stat-card.danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stat-content h3 {
    margin: 0 0 5px 0;
    font-size: 1rem;
    opacity: 0.9;
}

.stat-content p {
    margin: 0;
    font-size: 1.8rem;
    font-weight: bold;
}

/* Advanced Stats */
.advanced-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.chart-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-container h3 {
    margin: 0 0 20px 0;
    color: #007bff;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

/* Progress Charts */
.progress-chart {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.progress-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.progress-item span:first-child {
    min-width: 120px;
    font-size: 14px;
}

.progress-bar {
    flex: 1;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-fill.paid {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.progress-fill.partial {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.progress-fill.unpaid {
    background: linear-gradient(90deg, #dc3545, #e83e8c);
}

.progress-item span:last-child {
    min-width: 50px;
    text-align: center;
    font-weight: bold;
}

/* Transport Stats */
.transport-stats {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.transport-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 5px;
    border-right: 4px solid #007bff;
}

.transport-item span:last-child {
    font-weight: bold;
    color: #007bff;
}

/* Level Distribution */
.level-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.level-name {
    min-width: 120px;
    font-size: 14px;
}

.level-bar {
    flex: 1;
    height: 15px;
    background-color: #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.level-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 8px;
    transition: width 0.3s ease;
}

.level-count {
    min-width: 80px;
    text-align: center;
    font-weight: bold;
    color: #007bff;
}

/* Payment Tabs */
.payments-tabs {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
}

.tab-btn {
    background: none;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
    font-size: 14px;
}

.tab-btn.active {
    border-bottom-color: #007bff;
    color: #007bff;
    font-weight: bold;
}

.tab-btn:hover {
    background-color: #f8f9fa;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Additional Fees */
.additional-fees-section {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.additional-fees-section h4 {
    margin: 0 0 15px 0;
    color: #007bff;
}

.additional-fees-section form {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 10px;
    align-items: end;
}

.additional-fees-section input {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.additional-fees-section button {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}

.additional-fees-section button:hover {
    background-color: #218838;
}

.additional-fees-table {
    overflow-x: auto;
}

.additional-fees-table table {
    width: 100%;
    border-collapse: collapse;
}

.additional-fees-table th,
.additional-fees-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
}

.additional-fees-table th {
    background-color: #007bff;
    color: white;
}

.additional-fees-table .overdue {
    background-color: #fff5f5;
    border-right: 4px solid #dc3545;
}

.pay-additional-btn {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    margin: 2px;
    font-size: 12px;
}

.delete-additional-btn {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    margin: 2px;
    font-size: 12px;
}

.pay-additional-btn:hover {
    background-color: #218838;
}

.delete-additional-btn:hover {
    background-color: #c82333;
}

.no-additional-fees {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

@media (max-width: 768px) {
    .additional-fees-section form {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .payments-tabs {
        flex-direction: column;
    }

    .tab-btn {
        text-align: center;
        border-bottom: 1px solid #e9ecef;
        border-radius: 0;
    }

    .tab-btn.active {
        border-bottom-color: #007bff;
        background-color: #e7f3ff;
    }
}

/* Advanced Management Styles */
#advanced-management {
    margin-top: 30px;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#advanced-management h3 {
    color: #007bff;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.management-tabs {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.mgmt-tab-btn {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
    font-size: 14px;
    white-space: nowrap;
}

.mgmt-tab-btn.active {
    border-bottom-color: #007bff;
    color: #007bff;
    font-weight: bold;
}

.mgmt-tab-btn:hover {
    background-color: #f8f9fa;
}

.mgmt-tab-content {
    display: none;
}

.mgmt-tab-content.active {
    display: block;
}

.mgmt-tab-content h4 {
    color: #007bff;
    margin-bottom: 15px;
}

.mgmt-tab-content form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
}

.mgmt-tab-content form input,
.mgmt-tab-content form select,
.mgmt-tab-content form textarea {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.mgmt-tab-content form textarea {
    grid-column: 1 / -1;
    min-height: 80px;
    resize: vertical;
}

.mgmt-tab-content form button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    grid-column: 1 / -1;
    justify-self: start;
}

.mgmt-tab-content form button:hover {
    background-color: #0056b3;
}

/* Data Tables */
.data-table {
    overflow-x: auto;
    margin-top: 20px;
}

.data-table table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.data-table th,
.data-table td {
    border: 1px solid #ddd;
    padding: 10px;
    text-align: center;
    font-size: 14px;
}

.data-table th {
    background-color: #007bff;
    color: white;
    font-weight: bold;
}

.data-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.data-table tr:hover {
    background-color: #e7f3ff;
}

/* Rating Styles */
.rating-ممتاز {
    background-color: #d4edda;
    color: #155724;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

.rating-جيد-جداً {
    background-color: #d1ecf1;
    color: #0c5460;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

.rating-جيد {
    background-color: #fff3cd;
    color: #856404;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

.rating-مقبول {
    background-color: #ffeaa7;
    color: #6c5ce7;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

.rating-ضعيف {
    background-color: #f8d7da;
    color: #721c24;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

/* Note Type Styles */
.note-type-تأديبية {
    background-color: #f8d7da;
    color: #721c24;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

.note-type-تعليمية {
    background-color: #d4edda;
    color: #155724;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

.note-type-إدارية {
    background-color: #d1ecf1;
    color: #0c5460;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

.note-type-عامة {
    background-color: #e2e3e5;
    color: #383d41;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

.no-data {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 4px;
    margin: 20px 0;
}

@media (max-width: 768px) {
    .management-tabs {
        flex-direction: column;
    }

    .mgmt-tab-btn {
        text-align: center;
        border-bottom: 1px solid #e9ecef;
        border-radius: 0;
    }

    .mgmt-tab-btn.active {
        border-bottom-color: #007bff;
        background-color: #e7f3ff;
    }

    .mgmt-tab-content form {
        grid-template-columns: 1fr;
    }
}