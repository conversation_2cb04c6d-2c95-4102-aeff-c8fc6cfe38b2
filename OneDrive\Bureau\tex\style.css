body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
    color: #333;
    direction: rtl;
}

header {
   background-color: #007bff;
   color: white;
   padding: 1rem;
   box-shadow: 0 2px 4px rgba(0,0,0,0.1);
   display: flex;
   align-items: center;
   justify-content: center;
   gap: 15px;
}

header .logo {
   height: 50px;
   width: 50px;
}

header h1 {
    margin: 0;
    font-size: 2rem;
}

nav ul {
    list-style: none;
    padding: 0;
    margin: 1rem 0 0;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
}

nav a {
    color: white;
    text-decoration: none;
    padding: 10px 15px;
    border-radius: 5px;
    transition: background-color 0.3s;
}

nav a:hover, nav a.active {
    background-color: #0056b3;
}

main {
    padding: 1rem;
    max-width: 1200px;
    margin: 2rem auto;
}

section {
    background-color: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

h2 {
    color: #0056b3;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
    margin-top: 0;
}

h3 {
    color: #0069d9;
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}

form {
    margin-bottom: 2rem;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}

input[type="text"], input[type="number"], input[type="tel"], input[type="datetime-local"], select {
    flex-grow: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-width: 150px;
}

.form-full-width {
    width: 100%;
    margin-top: 10px;
}

.toolbar {
    display: flex;
    gap: 10px;
    margin-bottom: 1rem;
}

#search-student {
    flex-grow: 1;
}

button {
    background-color: #28a745;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #218838;
}

.secondary-btn {
    background-color: #6c757d;
}

.secondary-btn:hover {
    background-color: #5a6268;
}

.edit-btn {
    background-color: #007bff;
    margin-left: 5px;
}

.edit-btn:hover {
    background-color: #0056b3;
}

.delete-btn {
    background-color: #dc3545;
    margin-right: auto;
}

.delete-btn:hover {
    background-color: #c82333;
}

.toggle-status-btn {
    background-color: #ffc107;
    color: #333;
}

.toggle-status-btn:hover {
    background-color: #e0a800;
}

.paid {
    color: green;
    font-weight: bold;
}

.unpaid {
    color: red;
    font-weight: bold;
}

.whatsapp-btn {
    background-color: #25D366;
}

.whatsapp-btn:hover {
    background-color: #128C7E;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

th, td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
}

td button {
    margin-right: 5px;
}

td .student-picture-thumbnail {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

footer {
    text-align: center;
    padding: 1rem;
    margin-top: 2rem;
    background-color: #e9ecef;
    color: #6c757d;
}

#levels-and-groups-list .level-container {
    margin-bottom: 1.5rem;
    padding: 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
}

#levels-and-groups-list ul {
    list-style: none;
    padding: 0;
}

#levels-and-groups-list li {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#dashboard .stats-container, #activities-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

.stat-card, .activity-card {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
}

.activity-card h3 {
    margin-top: 0;
    font-size: 1.3rem;
    color: #0056b3;
}

.activity-card p {
    margin: 0.5rem 0;
}

.activity-card .media-container {
    margin-top: 1rem;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.activity-card .media-container img, .activity-card .media-container video {
    max-width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 5px;
}

#print-students-btn {
    margin-top: 1rem;
    background-color: #6c757d;
}

#print-students-btn:hover {
    background-color: #5a6268;
}

@media print {
    body * {
        visibility: hidden;
    }
    #student-list-container, #student-list-container * {
        visibility: visible;
    }
    #student-list-container {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
    #student-table .delete-btn, #student-table .edit-btn, #student-table .toggle-status-btn, #student-table .whatsapp-btn {
        display: none;
    }
    #student-table th:last-child, #student-table td:last-child {
        display: none;
    }
}

#data-management .restore-wrapper {
    margin-top: 1rem;
    border-top: 1px solid #ddd;
    padding-top: 1rem;
}

/* Styles for Detailed Teacher Form */
.detailed-form .form-row {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    width: 100%;
}

.detailed-form .form-row > * {
    flex: 1;
}

.detailed-form textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-height: 80px;
    resize: vertical;
}

.checklists-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
    padding: 1rem;
    border: 1px solid #eee;
    border-radius: 5px;
}

.checklist-group h4 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    color: #0056b3;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.checklist-group div {
    display: block;
    margin-bottom: 5px;
}

.checklist-group label {
    margin-right: 10px;
    cursor: pointer;
}

/* Styles for Subjects Display in Groups Page */
.subjects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.subject-category {
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 5px;
}

.subject-category h5 {
    margin-top: 0;
    color: #0056b3;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.subject-category ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.subject-category li {
    padding: 5px 0;
}

/* Search Section Styles */
#search-section {
    background: white;
    margin: 20px;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#search-section h2 {
    color: #007bff;
    margin-bottom: 20px;
    text-align: center;
}

.search-container {
    margin-bottom: 20px;
}

.search-fields {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.search-field {
    display: flex;
    flex-direction: column;
}

.search-field label {
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.search-field input,
.search-field select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

#search-btn,
#clear-search-btn {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 10px;
    font-size: 14px;
}

#clear-search-btn {
    background-color: #6c757d;
}

#search-btn:hover {
    background-color: #0056b3;
}

#clear-search-btn:hover {
    background-color: #545b62;
}

/* Search Results Styles */
#search-results {
    margin-top: 20px;
}

#search-results h3 {
    color: #007bff;
    margin-bottom: 15px;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

.result-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.result-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.student-card {
    border-right: 4px solid #28a745;
}

.teacher-card {
    border-right: 4px solid #007bff;
}

.result-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    gap: 10px;
}

.result-image {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #ddd;
}

.result-header h4 {
    margin: 0;
    flex-grow: 1;
    color: #333;
}

.result-type {
    background-color: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.student-card .result-type {
    background-color: #28a745;
}

.result-details p {
    margin: 5px 0;
    font-size: 14px;
}

.result-details strong {
    color: #007bff;
}

.paid {
    color: #28a745;
    font-weight: bold;
}

.unpaid {
    color: #dc3545;
    font-weight: bold;
}

.no-results {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

/* Modal Styles */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.modal-header {
    background-color: #007bff;
    color: white;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
}

.close-modal {
    font-size: 24px;
    cursor: pointer;
    font-weight: bold;
}

.close-modal:hover {
    opacity: 0.7;
}

.modal-body {
    padding: 20px;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

.cancel-btn {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
}

.cancel-btn:hover {
    background-color: #545b62;
}

/* Student Summary */
.student-summary {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.student-summary.enhanced {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.student-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #007bff;
}

.student-modal-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #007bff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.student-info h4 {
    margin: 0 0 8px 0;
    color: #007bff;
    font-size: 1.3rem;
}

.student-info p {
    margin: 4px 0;
    color: #6c757d;
    font-size: 14px;
}

.payment-summary {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.summary-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-right: 4px solid #007bff;
}

.summary-item.success {
    border-right-color: #28a745;
    background-color: #d4edda;
}

.summary-item.danger {
    border-right-color: #dc3545;
    background-color: #f8d7da;
}

.summary-item .label {
    font-weight: 500;
    color: #495057;
}

.summary-item .value {
    font-weight: bold;
    color: #007bff;
    font-size: 1.1rem;
}

.summary-item.success .value {
    color: #28a745;
}

.summary-item.danger .value {
    color: #dc3545;
}

.months-summary {
    display: flex;
    justify-content: space-around;
    gap: 10px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

.month-stat {
    text-align: center;
    padding: 10px;
    border-radius: 6px;
    min-width: 80px;
}

.month-stat.paid {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
}

.month-stat.partial {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
}

.month-stat.unpaid {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
}

.month-stat .count {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 4px;
}

.month-stat.paid .count {
    color: #28a745;
}

.month-stat.partial .count {
    color: #856404;
}

.month-stat.unpaid .count {
    color: #dc3545;
}

.month-stat .label {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.student-summary h4 {
    margin: 0 0 10px 0;
    color: #007bff;
}

.student-summary p {
    margin: 5px 0;
}

/* Payments Header */
.payments-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
    margin-bottom: 0;
}

.payments-header h4 {
    margin: 0 0 5px 0;
    font-size: 1.2rem;
}

.payments-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 14px;
}

/* Payments Table */
.payments-table {
    overflow-x: auto;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
    border-top: none;
}

.payments-table table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    margin: 0;
}

.payments-table th,
.payments-table td {
    border: 1px solid #dee2e6;
    padding: 12px 8px;
    text-align: center;
    font-size: 14px;
}

.payments-table th {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 10;
}

/* Payment Row Styles */
.payment-row-مدفوع {
    background-color: #d4edda;
}

.payment-row-جزئي {
    background-color: #fff3cd;
}

.payment-row-غير-مدفوع {
    background-color: #f8d7da;
}

.payments-table tr:hover {
    background-color: #e7f3ff !important;
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* Month Cell */
.month-cell {
    text-align: right !important;
    padding: 15px !important;
}

.month-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.month-name {
    font-weight: bold;
    color: #007bff;
    font-size: 16px;
}

.month-number {
    font-size: 12px;
    color: #6c757d;
    margin-top: 2px;
}

/* Amount Cells */
.amount-cell,
.paid-cell {
    font-weight: bold;
    color: #007bff;
}

.remaining-cell {
    font-weight: bold;
}

.remaining-cell.has-remaining {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
}

.remaining-cell.no-remaining {
    color: #28a745;
    background-color: rgba(40, 167, 69, 0.1);
}

/* Status Badge */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.status-مدفوع {
    background-color: #28a745;
    color: white;
}

.status-badge.status-جزئي {
    background-color: #ffc107;
    color: #212529;
}

.status-badge.status-غير-مدفوع {
    background-color: #dc3545;
    color: white;
}

/* Actions Cell */
.actions-cell {
    padding: 8px !important;
}

.pay-btn,
.partial-pay-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    margin: 2px;
    font-size: 11px;
    font-weight: bold;
    transition: all 0.2s ease;
}

.partial-pay-btn {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
}

.pay-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.partial-pay-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.paid-indicator {
    color: #28a745;
    font-weight: bold;
    font-size: 12px;
}

.payments-table .status-مدفوع {
    background-color: #d4edda;
    color: #155724;
    font-weight: bold;
}

.payments-table .status-جزئي {
    background-color: #fff3cd;
    color: #856404;
    font-weight: bold;
}

.payments-table .status-غير-مدفوع {
    background-color: #f8d7da;
    color: #721c24;
    font-weight: bold;
}

.pay-btn,
.partial-pay-btn {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    margin: 2px;
    font-size: 12px;
}

.partial-pay-btn {
    background-color: #ffc107;
    color: #212529;
}

.pay-btn:hover {
    background-color: #218838;
}

.partial-pay-btn:hover {
    background-color: #e0a800;
}

/* Transport Status */
.transport-yes {
    color: #28a745;
    font-weight: bold;
}

.transport-no {
    color: #6c757d;
}

/* Enhanced Table Styles */
#student-table th {
    background-color: #007bff;
    color: white;
    font-weight: bold;
    text-align: center;
}

#student-table td {
    text-align: center;
    vertical-align: middle;
}

.student-picture-thumbnail {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #ddd;
}

.payments-btn {
    background-color: #17a2b8;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    margin: 2px;
    font-size: 12px;
}

.payments-btn:hover {
    background-color: #138496;
}

.whatsapp-btn {
    background-color: #25d366;
    color: white;
    border: none;
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    margin: 2px;
    transition: background-color 0.2s ease;
}

.whatsapp-btn:hover {
    background-color: #128c7e;
}

.whatsapp-btn i {
    font-size: 14px;
}

/* Enhanced Dashboard Stats */
.stat-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-card.primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.stat-card.secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
}

.stat-card.success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.stat-card.warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

.stat-card.info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.stat-card.danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stat-content h3 {
    margin: 0 0 5px 0;
    font-size: 1rem;
    opacity: 0.9;
}

.stat-content p {
    margin: 0;
    font-size: 1.8rem;
    font-weight: bold;
}

/* Advanced Stats */
.advanced-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.chart-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-container h3 {
    margin: 0 0 20px 0;
    color: #007bff;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

/* Progress Charts */
.progress-chart {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.progress-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.progress-item span:first-child {
    min-width: 120px;
    font-size: 14px;
}

.progress-bar {
    flex: 1;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-fill.paid {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.progress-fill.partial {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.progress-fill.unpaid {
    background: linear-gradient(90deg, #dc3545, #e83e8c);
}

.progress-item span:last-child {
    min-width: 50px;
    text-align: center;
    font-weight: bold;
}

/* Transport Stats */
.transport-stats {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.transport-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 5px;
    border-right: 4px solid #007bff;
}

.transport-item span:last-child {
    font-weight: bold;
    color: #007bff;
}

/* Level Distribution */
.level-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.level-name {
    min-width: 120px;
    font-size: 14px;
}

.level-bar {
    flex: 1;
    height: 15px;
    background-color: #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.level-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 8px;
    transition: width 0.3s ease;
}

.level-count {
    min-width: 80px;
    text-align: center;
    font-weight: bold;
    color: #007bff;
}

/* Payment Tabs */
.payments-tabs {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
}

.tab-btn {
    background: none;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
    font-size: 14px;
}

.tab-btn.active {
    border-bottom-color: #007bff;
    color: #007bff;
    font-weight: bold;
}

.tab-btn:hover {
    background-color: #f8f9fa;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Additional Fees */
.additional-fees-section {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.additional-fees-section h4 {
    margin: 0 0 15px 0;
    color: #007bff;
}

.additional-fees-section form {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 10px;
    align-items: end;
}

.additional-fees-section input {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.additional-fees-section button {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}

.additional-fees-section button:hover {
    background-color: #218838;
}

.additional-fees-table {
    overflow-x: auto;
}

.additional-fees-table table {
    width: 100%;
    border-collapse: collapse;
}

.additional-fees-table th,
.additional-fees-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
}

.additional-fees-table th {
    background-color: #007bff;
    color: white;
}

.additional-fees-table .overdue {
    background-color: #fff5f5;
    border-right: 4px solid #dc3545;
}

.pay-additional-btn {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    margin: 2px;
    font-size: 12px;
}

.delete-additional-btn {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    margin: 2px;
    font-size: 12px;
}

.pay-additional-btn:hover {
    background-color: #218838;
}

.delete-additional-btn:hover {
    background-color: #c82333;
}

.no-additional-fees {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

@media (max-width: 768px) {
    .additional-fees-section form {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .payments-tabs {
        flex-direction: column;
    }

    .tab-btn {
        text-align: center;
        border-bottom: 1px solid #e9ecef;
        border-radius: 0;
    }

    .tab-btn.active {
        border-bottom-color: #007bff;
        background-color: #e7f3ff;
    }
}

/* Advanced Management Styles */
#advanced-management {
    margin-top: 30px;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#advanced-management h3 {
    color: #007bff;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.management-tabs {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.mgmt-tab-btn {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
    font-size: 14px;
    white-space: nowrap;
}

.mgmt-tab-btn.active {
    border-bottom-color: #007bff;
    color: #007bff;
    font-weight: bold;
}

.mgmt-tab-btn:hover {
    background-color: #f8f9fa;
}

.mgmt-tab-content {
    display: none;
}

.mgmt-tab-content.active {
    display: block;
}

.mgmt-tab-content h4 {
    color: #007bff;
    margin-bottom: 15px;
}

.mgmt-tab-content form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
}

.mgmt-tab-content form input,
.mgmt-tab-content form select,
.mgmt-tab-content form textarea {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.mgmt-tab-content form textarea {
    grid-column: 1 / -1;
    min-height: 80px;
    resize: vertical;
}

.mgmt-tab-content form button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    grid-column: 1 / -1;
    justify-self: start;
}

.mgmt-tab-content form button:hover {
    background-color: #0056b3;
}

/* Data Tables */
.data-table {
    overflow-x: auto;
    margin-top: 20px;
}

.data-table table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.data-table th,
.data-table td {
    border: 1px solid #ddd;
    padding: 10px;
    text-align: center;
    font-size: 14px;
}

.data-table th {
    background-color: #007bff;
    color: white;
    font-weight: bold;
}

.data-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.data-table tr:hover {
    background-color: #e7f3ff;
}

/* Rating Styles */
.rating-ممتاز {
    background-color: #d4edda;
    color: #155724;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

.rating-جيد-جداً {
    background-color: #d1ecf1;
    color: #0c5460;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

.rating-جيد {
    background-color: #fff3cd;
    color: #856404;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

.rating-مقبول {
    background-color: #ffeaa7;
    color: #6c5ce7;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

.rating-ضعيف {
    background-color: #f8d7da;
    color: #721c24;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

/* Note Type Styles */
.note-type-تأديبية {
    background-color: #f8d7da;
    color: #721c24;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

.note-type-تعليمية {
    background-color: #d4edda;
    color: #155724;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

.note-type-إدارية {
    background-color: #d1ecf1;
    color: #0c5460;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

.note-type-عامة {
    background-color: #e2e3e5;
    color: #383d41;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
}

.no-data {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 4px;
    margin: 20px 0;
}

@media (max-width: 768px) {
    .management-tabs {
        flex-direction: column;
    }

    .mgmt-tab-btn {
        text-align: center;
        border-bottom: 1px solid #e9ecef;
        border-radius: 0;
    }

    .mgmt-tab-btn.active {
        border-bottom-color: #007bff;
        background-color: #e7f3ff;
    }

    .mgmt-tab-content form {
        grid-template-columns: 1fr;
    }
}

/* Staff Management Styles */
.staff-tabs {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.staff-tab-btn {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
    font-size: 14px;
    white-space: nowrap;
}

.staff-tab-btn.active {
    border-bottom-color: #007bff;
    color: #007bff;
    font-weight: bold;
}

.staff-tab-btn:hover {
    background-color: #f8f9fa;
}

.staff-tab-content {
    display: none;
}

.staff-tab-content.active {
    display: block;
}

.staff-tab-content h3 {
    color: #007bff;
    margin-bottom: 20px;
}

.staff-tab-content form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
}

.staff-tab-content form input,
.staff-tab-content form select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.staff-tab-content form button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    grid-column: 1 / -1;
    justify-self: start;
}

.staff-tab-content form button:hover {
    background-color: #0056b3;
}

/* Staff Tables */
.staff-table {
    overflow-x: auto;
    margin-top: 20px;
}

.staff-table table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.staff-table th,
.staff-table td {
    border: 1px solid #ddd;
    padding: 10px;
    text-align: center;
    font-size: 14px;
}

.staff-table th {
    background-color: #007bff;
    color: white;
    font-weight: bold;
}

.staff-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.staff-table tr:hover {
    background-color: #e7f3ff;
}

.salary-btn {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    margin: 2px;
    font-size: 12px;
}

.salary-btn:hover {
    background-color: #218838;
}

/* Salary Management */
.salary-management {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.salary-summary {
    margin-bottom: 30px;
}

.salary-summary h4 {
    color: #007bff;
    margin-bottom: 15px;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.summary-card {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
}

.summary-card h5 {
    margin: 0 0 10px 0;
    font-size: 14px;
    opacity: 0.9;
}

.summary-card p {
    margin: 0;
    font-size: 1.5rem;
    font-weight: bold;
}

/* Financial Page Styles */
.financial-tabs {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.financial-tab-btn {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
    font-size: 14px;
    white-space: nowrap;
}

.financial-tab-btn.active {
    border-bottom-color: #007bff;
    color: #007bff;
    font-weight: bold;
}

.financial-tab-btn:hover {
    background-color: #f8f9fa;
}

.financial-tab-content {
    display: none;
}

.financial-tab-content.active {
    display: block;
}

.financial-tab-content h3 {
    color: #007bff;
    margin-bottom: 20px;
}

/* Quick Search */
.quick-search {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.quick-search input,
.quick-search select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

/* Financial Summary */
.financial-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.financial-summary .summary-card {
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    color: white;
}

.financial-summary .summary-card.total {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.financial-summary .summary-card.paid {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.financial-summary .summary-card.remaining {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.financial-summary .summary-card.percentage {
    background: linear-gradient(135deg, #17a2b8, #138496);
}

.financial-summary .summary-card h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    opacity: 0.9;
}

.financial-summary .summary-card p {
    margin: 0;
    font-size: 1.8rem;
    font-weight: bold;
}

/* Financial Table */
.financial-table {
    overflow-x: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
}

.financial-table table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    margin: 0;
}

.financial-table th,
.financial-table td {
    border: 1px solid #dee2e6;
    padding: 12px 8px;
    text-align: center;
    font-size: 14px;
}

.financial-table th {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 10;
}

.financial-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.financial-table tr:hover {
    background-color: #e7f3ff;
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* Action Buttons */
.quick-pay-btn,
.view-details-btn,
.salary-pay-btn,
.transport-pay-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    margin: 2px;
    font-size: 11px;
    font-weight: bold;
    transition: all 0.2s ease;
}

.view-details-btn {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.salary-pay-btn {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
}

.transport-pay-btn {
    background: linear-gradient(135deg, #17a2b8, #138496);
}

.quick-pay-btn:hover,
.view-details-btn:hover,
.salary-pay-btn:hover,
.transport-pay-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Staff Categories */
.staff-categories {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.category-tabs {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
}

.category-btn {
    background: none;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
}

.category-btn.active {
    border-bottom-color: #007bff;
    color: #007bff;
    font-weight: bold;
}

.category-btn:hover {
    background-color: #f8f9fa;
}

/* Reports Section */
.reports-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.report-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    align-items: center;
}

.report-filters select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.report-filters button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

.report-filters button:hover {
    background-color: #0056b3;
}

/* Bulk Payment Styles */
.bulk-payment-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    border: 2px solid #007bff;
}

.bulk-payment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.bulk-payment-header h4 {
    margin: 0;
    color: #007bff;
    font-size: 1.2rem;
}

.bulk-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.select-all-btn,
.clear-btn,
.bulk-pay-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.2s ease;
}

.select-all-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.clear-btn {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
}

.bulk-pay-btn {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.bulk-pay-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
}

.select-all-btn:hover,
.clear-btn:hover,
.bulk-pay-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.selected-preview {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    background-color: #f8f9fa;
}

.selected-student-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 10px;
    margin: 2px 0;
    background: white;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.student-select {
    transform: scale(1.2);
    margin-left: 5px;
}

/* Fee Breakdown */
.fee-breakdown {
    text-align: right;
    font-size: 12px;
}

.fee-breakdown div {
    margin: 2px 0;
}

.total-fee {
    border-top: 1px solid #dee2e6;
    padding-top: 4px;
    margin-top: 4px;
}

/* Large Modal */
.large-modal {
    max-width: 800px;
    width: 90%;
}

.bulk-payment-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    border: 1px solid #dee2e6;
}

.bulk-students-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    background: white;
}

.bulk-student-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    margin: 4px 0;
    background: #e7f3ff;
    border-radius: 4px;
    border: 1px solid #007bff;
}

.bulk-summary {
    background: #e7f3ff;
    padding: 15px;
    border-radius: 4px;
    margin: 15px 0;
    border: 1px solid #007bff;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin: 5px 0;
    font-weight: bold;
}

.receipt-btn {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.2s ease;
}

.receipt-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

@media (max-width: 768px) {
    .financial-tabs {
        flex-direction: column;
    }

    .financial-tab-btn {
        text-align: center;
        border-bottom: 1px solid #e9ecef;
        border-radius: 0;
    }

    .financial-tab-btn.active {
        border-bottom-color: #007bff;
        background-color: #e7f3ff;
    }

    .quick-search {
        grid-template-columns: 1fr;
    }

    .financial-summary {
        grid-template-columns: 1fr;
    }

    .report-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .bulk-payment-header {
        flex-direction: column;
        align-items: stretch;
    }

    .bulk-actions {
        justify-content: center;
    }

    .large-modal {
        width: 95%;
        max-width: none;
    }
}

/* Enhanced Checklist Styles */
.form-section {
    margin-bottom: 25px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.form-section h3,
.form-section h4 {
    margin: 0 0 15px 0;
    color: #007bff;
    font-size: 1.1rem;
    border-bottom: 2px solid #007bff;
    padding-bottom: 8px;
}

.checkbox-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.checkbox-item:hover {
    background-color: #e7f3ff;
    border-color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.checkbox-item input[type="checkbox"] {
    margin-left: 8px;
    transform: scale(1.2);
    accent-color: #007bff;
}

.checkbox-item label {
    cursor: pointer;
    font-size: 14px;
    color: #495057;
    user-select: none;
    flex: 1;
}

.checkbox-item input[type="checkbox"]:checked + label {
    color: #007bff;
    font-weight: bold;
}

.no-items {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    padding: 20px;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    margin: 10px 0;
}

/* Enhanced Teacher Form */
#add-teacher-form {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

#add-teacher-form input,
#add-teacher-form textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 15px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

#add-teacher-form input:focus,
#add-teacher-form textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

#add-teacher-form button[type="submit"] {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.2s ease;
    width: 100%;
    margin-top: 20px;
}

#add-teacher-form button[type="submit"]:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

@media (max-width: 768px) {
    .checkbox-list {
        grid-template-columns: 1fr;
    }

    .form-section {
        padding: 10px;
    }

    .checkbox-item {
        padding: 6px 10px;
    }
}

/* Annual Fees Styles */
.annual-fees {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border: 2px solid #ffc107;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.annual-fees h4 {
    color: #856404;
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    text-align: center;
    border-bottom: 2px solid #ffc107;
    padding-bottom: 8px;
}

.annual-fees-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.fee-item {
    background: white;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #ffc107;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.fee-item label {
    display: block;
    font-weight: bold;
    color: #856404;
    margin-bottom: 8px;
    font-size: 14px;
}

.fee-item input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ffc107;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.fee-item input:focus {
    outline: none;
    border-color: #fd7e14;
    box-shadow: 0 0 0 2px rgba(255,193,7,0.25);
}

.fee-item input::placeholder {
    color: #6c757d;
    font-style: italic;
}

/* Annual Fees in Payments Modal */
.annual-fees-section {
    padding: 20px;
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border-radius: 8px;
    border: 2px solid #ffc107;
}

.annual-fees-section h4 {
    color: #856404;
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.2rem;
    border-bottom: 2px solid #ffc107;
    padding-bottom: 10px;
}

.annual-fee-item {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #ffc107;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 15px;
    transition: transform 0.2s ease;
}

.annual-fee-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.fee-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.fee-header h5 {
    margin: 0;
    color: #856404;
    font-size: 1.1rem;
}

.fee-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.fee-status.paid {
    background-color: #28a745;
    color: white;
}

.fee-status.unpaid {
    background-color: #dc3545;
    color: white;
}

.fee-amount {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
    text-align: center;
    margin: 15px 0;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.pay-annual-fee-btn {
    width: 100%;
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
    border: none;
    padding: 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.2s ease;
}

.pay-annual-fee-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.pay-annual-fee-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
}

/* Payments Operations Styles */
.payments-filters {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-weight: bold;
    color: #333;
    font-size: 14px;
}

.filter-group input,
.filter-group select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.filter-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.payments-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-card.total {
    border-left: 4px solid #007bff;
}

.stat-card.income {
    border-left: 4px solid #28a745;
}

.stat-card.today {
    border-left: 4px solid #ffc107;
}

.stat-card.month {
    border-left: 4px solid #17a2b8;
}

.stat-icon {
    font-size: 2rem;
    color: #6c757d;
}

.stat-info h3 {
    margin: 0 0 5px 0;
    font-size: 14px;
    color: #6c757d;
    text-transform: uppercase;
}

.stat-info p {
    margin: 0;
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
}

.reports-tabs {
    display: flex;
    background: white;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.report-tab-btn {
    flex: 1;
    padding: 15px 20px;
    background: #f8f9fa;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    color: #6c757d;
    transition: all 0.2s ease;
    border-bottom: 3px solid transparent;
}

.report-tab-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.report-tab-btn.active {
    background: white;
    color: #007bff;
    border-bottom-color: #007bff;
}

.report-tab-content {
    display: none;
    background: white;
    padding: 30px;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.report-tab-content.active {
    display: block;
}

.operations-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f8f9fa;
}

.operations-header h3 {
    margin: 0;
    color: #333;
}

.operations-actions {
    display: flex;
    gap: 10px;
}

.refresh-btn {
    background: #17a2b8;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s ease;
}

.refresh-btn:hover {
    background: #138496;
}

.operations-table {
    overflow-x: auto;
}

.operations-table table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.operations-table th,
.operations-table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #dee2e6;
}

.operations-table th {
    background: #f8f9fa;
    font-weight: bold;
    color: #495057;
    position: sticky;
    top: 0;
}

.operations-table tr:hover {
    background: #f8f9fa;
}

.date-selector,
.month-selector,
.year-selector {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.date-selector label,
.month-selector label,
.year-selector label {
    font-weight: bold;
    color: #495057;
}

.date-selector input,
.month-selector select,
.month-selector input,
.year-selector input {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

@media (max-width: 768px) {
    .annual-fees-grid {
        grid-template-columns: 1fr;
    }

    .fee-item {
        padding: 10px;
    }

    .fee-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .annual-fee-item {
        padding: 15px;
    }

    .filter-row {
        grid-template-columns: 1fr;
    }

    .filter-actions {
        justify-content: center;
    }

    .payments-stats {
        grid-template-columns: 1fr;
    }

    .reports-tabs {
        flex-direction: column;
    }

    .operations-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .date-selector,
    .month-selector,
    .year-selector {
        flex-direction: column;
        align-items: stretch;
    }
}

/* WhatsApp Management Styles */
.whatsapp-tabs {
    display: flex;
    background: white;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 0;
}

.whatsapp-tab-btn {
    flex: 1;
    padding: 15px 20px;
    background: #f8f9fa;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    color: #6c757d;
    transition: all 0.2s ease;
    border-bottom: 3px solid transparent;
}

.whatsapp-tab-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.whatsapp-tab-btn.active {
    background: #25d366;
    color: white;
    border-bottom-color: #128c7e;
}

.whatsapp-tab-content {
    display: none;
    background: white;
    padding: 30px;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.whatsapp-tab-content.active {
    display: block;
}

.chat-search {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.chat-search input {
    flex: 1;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 25px;
    font-size: 14px;
    outline: none;
}

.chat-search input:focus {
    border-color: #25d366;
    box-shadow: 0 0 0 2px rgba(37, 211, 102, 0.2);
}

.search-btn {
    background: #25d366;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.search-btn:hover {
    background: #128c7e;
}

.contacts-list h4,
.groups-list h4 {
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
}

.contacts-grid,
.groups-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

.contact-card,
.group-card {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
    cursor: pointer;
}

.contact-card:hover,
.group-card:hover {
    background: #e9ecef;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.contact-info,
.group-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 10px;
}

.contact-avatar,
.group-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #25d366;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 18px;
}

.contact-details h5,
.group-details h5 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 16px;
}

.contact-details p,
.group-details p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
}

.contact-actions,
.group-actions {
    display: flex;
    gap: 8px;
}

.whatsapp-btn {
    background: #25d366;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 15px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.2s ease;
}

.whatsapp-btn:hover {
    background: #128c7e;
}

.groups-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.recipient-selection {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.recipient-selection h4 {
    margin: 0 0 15px 0;
    color: #333;
}

.selection-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.selection-options label {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.selection-options label:hover {
    background: #e9ecef;
}

.conditional-selection {
    margin-top: 15px;
    padding: 15px;
    background: white;
    border-radius: 6px;
}

.conditional-selection select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.custom-contacts-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
}

.contacts-checklist {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 10px;
}

.contacts-checklist label {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    cursor: pointer;
}

.message-composition {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
}

.message-composition h4 {
    margin: 0 0 15px 0;
    color: #333;
}

.message-options {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.template-btn,
.attachment-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s ease;
}

.template-btn:hover,
.attachment-btn:hover {
    background: #5a6268;
}

.message-composition textarea {
    width: 100%;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    margin-bottom: 15px;
}

.message-preview {
    margin-bottom: 15px;
}

.message-preview h5 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 14px;
}

.preview-box {
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #ddd;
    min-height: 60px;
    color: #6c757d;
    font-style: italic;
}

.send-options {
    margin-bottom: 20px;
}

.send-options label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
}

.send-btn {
    background: #25d366;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: background 0.2s ease;
    width: 100%;
}

.send-btn:hover {
    background: #128c7e;
}

.templates-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.templates-categories {
    margin-bottom: 20px;
}

.category-tabs {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.category-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.category-btn:hover {
    background: #e9ecef;
}

.category-btn.active {
    background: #25d366;
    color: white;
    border-color: #25d366;
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.template-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.template-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.template-header h5 {
    margin: 0;
    color: #333;
    font-size: 16px;
}

.template-category-badge {
    background: #6c757d;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.template-content {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
    max-height: 100px;
    overflow: hidden;
}

.template-actions {
    display: flex;
    gap: 8px;
}

.use-template-btn {
    background: #25d366;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    flex: 1;
}

.edit-template-btn,
.delete-template-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.delete-template-btn {
    background: #dc3545;
}

@media (max-width: 768px) {
    .whatsapp-tabs {
        flex-direction: column;
    }

    .contacts-grid,
    .groups-grid,
    .templates-grid {
        grid-template-columns: 1fr;
    }

    .selection-options {
        grid-template-columns: 1fr;
    }

    .contacts-checklist {
        grid-template-columns: 1fr;
    }

    .message-options {
        flex-direction: column;
    }

    .groups-actions,
    .templates-actions {
        flex-direction: column;
    }

    .category-tabs {
        justify-content: center;
    }
}

/* Import Export Styles */
.import-export-tabs {
    display: flex;
    background: white;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 0;
}

.ie-tab-btn {
    flex: 1;
    padding: 15px 20px;
    background: #f8f9fa;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    color: #6c757d;
    transition: all 0.2s ease;
    border-bottom: 3px solid transparent;
}

.ie-tab-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.ie-tab-btn.active {
    background: white;
    color: #007bff;
    border-bottom-color: #007bff;
}

.ie-tab-content {
    display: none;
    background: white;
    padding: 30px;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ie-tab-content.active {
    display: block;
}

.section-description {
    color: #6c757d;
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 30px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #007bff;
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
}

.template-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.template-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.template-icon {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 20px;
    text-align: center;
    font-size: 2.5rem;
}

.template-info {
    padding: 20px;
}

.template-info h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1.2rem;
}

.template-info p {
    color: #6c757d;
    margin-bottom: 15px;
    line-height: 1.5;
}

.template-features {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
}

.template-features li {
    padding: 5px 0;
    color: #495057;
    font-size: 14px;
    position: relative;
    padding-right: 20px;
}

.template-features li::before {
    content: "✓";
    color: #28a745;
    font-weight: bold;
    position: absolute;
    right: 0;
}

.download-template-btn {
    width: 100%;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.2s ease;
}

.download-template-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.import-type-selector {
    margin-bottom: 30px;
}

.import-type-selector h4 {
    margin-bottom: 15px;
    color: #333;
}

.import-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.import-type-option {
    cursor: pointer;
}

.import-type-option input[type="radio"] {
    display: none;
}

.option-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.import-type-option input[type="radio"]:checked + .option-content {
    background: #e3f2fd;
    border-color: #007bff;
    color: #007bff;
}

.option-content i {
    font-size: 2rem;
    margin-bottom: 10px;
}

.option-content span {
    font-weight: bold;
    font-size: 14px;
}

.file-upload-area {
    margin: 30px 0;
}

.upload-zone {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.2s ease;
}

.upload-zone:hover,
.upload-zone.dragover {
    border-color: #007bff;
    background: #e3f2fd;
}

.upload-icon {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 15px;
}

.upload-text h4 {
    margin: 0 0 10px 0;
    color: #333;
}

.upload-text p {
    margin: 0 0 5px 0;
    color: #6c757d;
}

.upload-text small {
    color: #6c757d;
    font-size: 12px;
}

.import-options-settings {
    margin: 30px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.import-options-settings h4 {
    margin: 0 0 15px 0;
    color: #333;
}

.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.option-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px;
    background: white;
    border-radius: 6px;
    cursor: pointer;
}

.import-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

.import-preview,
.import-progress,
.import-results {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.import-preview h4,
.import-progress h4,
.import-results h4 {
    margin: 0 0 15px 0;
    color: #333;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    font-weight: bold;
    color: #007bff;
}

.export-categories {
    margin-bottom: 30px;
}

.export-categories h4 {
    margin-bottom: 20px;
    color: #333;
}

.export-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.export-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.export-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.export-header i {
    font-size: 1.5rem;
    color: #007bff;
}

.export-header h5 {
    margin: 0;
    color: #333;
}

.export-options-list {
    margin-bottom: 20px;
}

.export-options-list label {
    display: block;
    margin-bottom: 8px;
    padding: 5px 0;
    cursor: pointer;
}

.export-btn {
    width: 100%;
    background: #007bff;
    color: white;
    border: none;
    padding: 10px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    transition: background 0.2s ease;
}

.export-btn:hover {
    background: #0056b3;
}

.export-settings {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.export-settings h4 {
    margin: 0 0 15px 0;
    color: #333;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.setting-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.setting-group label {
    font-weight: bold;
    color: #495057;
    font-size: 14px;
}

.setting-group select,
.setting-group input {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.backup-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.backup-create,
.backup-restore {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.backup-create h4,
.backup-restore h4 {
    margin: 0 0 15px 0;
    color: #333;
}

.backup-options {
    margin-bottom: 20px;
}

.backup-options label {
    display: block;
    margin-bottom: 8px;
    padding: 5px 0;
    cursor: pointer;
}

.restore-upload {
    margin-bottom: 15px;
}

.backup-history {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.backup-history h4 {
    margin: 0 0 15px 0;
    color: #333;
}

.backup-list {
    max-height: 300px;
    overflow-y: auto;
}

.backup-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.backup-item:last-child {
    border-bottom: none;
}

.backup-info {
    flex: 1;
}

.backup-info h6 {
    margin: 0 0 5px 0;
    color: #333;
}

.backup-info p {
    margin: 0;
    color: #6c757d;
    font-size: 12px;
}

.backup-actions-item {
    display: flex;
    gap: 5px;
}

.restore-btn,
.download-backup-btn,
.delete-backup-btn {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.restore-btn {
    background: #28a745;
    color: white;
}

.download-backup-btn {
    background: #007bff;
    color: white;
}

.delete-backup-btn {
    background: #dc3545;
    color: white;
}

@media (max-width: 768px) {
    .import-export-tabs {
        flex-direction: column;
    }

    .templates-grid,
    .export-grid {
        grid-template-columns: 1fr;
    }

    .import-types {
        grid-template-columns: repeat(2, 1fr);
    }

    .options-grid,
    .settings-grid {
        grid-template-columns: 1fr;
    }

    .import-actions {
        flex-direction: column;
    }

    .backup-actions {
        grid-template-columns: 1fr;
    }
}